#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTorch神经网络学习 - 第七部分
包含：神经网络基础、层定义、前向传播、损失函数、优化器等
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt

print("=" * 50)
print("PyTorch神经网络学习")
print("=" * 50)

# 1. 神经网络基础概念
print("\n1. 神经网络基础概念")
print("-" * 30)
print("""
神经网络的基本组成：
- 神经元 (Neuron): 基本计算单元
- 层 (Layer): 神经元的集合
- 权重 (Weight): 连接的强度
- 偏置 (Bias): 偏移量
- 激活函数 (Activation): 非线性变换
- 损失函数 (Loss): 衡量预测与真实值的差异
- 优化器 (Optimizer): 更新权重的算法
""")

# 2. 使用nn.Module创建神经网络
print("\n2. 使用nn.Module创建神经网络")
print("-" * 30)

class SimpleNet(nn.Module):
    """简单的全连接神经网络"""
    
    def __init__(self, input_size, hidden_size, output_size):
        super(SimpleNet, self).__init__()
        # 定义网络层
        self.fc1 = nn.Linear(input_size, hidden_size)  # 第一个全连接层
        self.fc2 = nn.Linear(hidden_size, hidden_size) # 第二个全连接层
        self.fc3 = nn.Linear(hidden_size, output_size) # 输出层
        
    def forward(self, x):
        """前向传播"""
        # 第一层 + ReLU激活
        x = F.relu(self.fc1(x))
        # 第二层 + ReLU激活
        x = F.relu(self.fc2(x))
        # 输出层（不使用激活函数）
        x = self.fc3(x)
        return x

# 创建网络实例
net = SimpleNet(input_size=10, hidden_size=20, output_size=3)
print(f"网络结构:\n{net}")

# 查看网络参数
total_params = sum(p.numel() for p in net.parameters())
trainable_params = sum(p.numel() for p in net.parameters() if p.requires_grad)
print(f"\n网络参数:")
print(f"总参数数量: {total_params}")
print(f"可训练参数数量: {trainable_params}")

# 3. 前向传播示例
print("\n3. 前向传播示例")
print("-" * 30)

# 创建随机输入
batch_size = 5
input_data = torch.randn(batch_size, 10)
print(f"输入数据形状: {input_data.shape}")

# 前向传播
with torch.no_grad():  # 不计算梯度
    output = net(input_data)
    print(f"输出数据形状: {output.shape}")
    print(f"输出数据:\n{output}")

# 4. 常用的神经网络层
print("\n4. 常用的神经网络层")
print("-" * 30)

class LayerDemo(nn.Module):
    """演示各种常用层"""
    
    def __init__(self):
        super(LayerDemo, self).__init__()
        
        # 全连接层
        self.linear = nn.Linear(10, 5)
        
        # 卷积层 (用于图像)
        self.conv1d = nn.Conv1d(1, 3, kernel_size=3)
        self.conv2d = nn.Conv2d(1, 3, kernel_size=3)
        
        # 池化层
        self.maxpool1d = nn.MaxPool1d(2)
        self.avgpool2d = nn.AvgPool2d(2)
        
        # 批归一化
        self.batchnorm1d = nn.BatchNorm1d(5)
        self.batchnorm2d = nn.BatchNorm2d(3)
        
        # Dropout (防止过拟合)
        self.dropout = nn.Dropout(0.5)
        
        # RNN层
        self.lstm = nn.LSTM(10, 20, batch_first=True)
        
    def forward(self, x):
        # 这里只是演示，实际使用时需要根据数据形状选择合适的层
        return x

layer_demo = LayerDemo()
print("常用层类型:")
for name, module in layer_demo.named_modules():
    if name:  # 跳过根模块
        print(f"  {name}: {type(module).__name__}")

# 5. 激活函数
print("\n5. 激活函数")
print("-" * 30)

# 创建测试数据
x = torch.linspace(-3, 3, 100)

# 常用激活函数
activations = {
    'ReLU': F.relu(x),
    'Sigmoid': torch.sigmoid(x),
    'Tanh': torch.tanh(x),
    'LeakyReLU': F.leaky_relu(x, 0.1),
    'ELU': F.elu(x),
    'Softmax': F.softmax(x.unsqueeze(0), dim=1).squeeze(0)
}

print("激活函数在x=0处的值:")
for name, activation in activations.items():
    mid_idx = len(x) // 2  # x=0的索引
    print(f"  {name}: {activation[mid_idx]:.4f}")

# 6. 损失函数
print("\n6. 损失函数")
print("-" * 30)

# 回归问题的损失函数
print("回归损失函数:")
predictions = torch.tensor([1.0, 2.0, 3.0])
targets = torch.tensor([1.5, 2.2, 2.8])

mse_loss = F.mse_loss(predictions, targets)
mae_loss = F.l1_loss(predictions, targets)
smooth_l1_loss = F.smooth_l1_loss(predictions, targets)

print(f"  预测值: {predictions}")
print(f"  真实值: {targets}")
print(f"  MSE损失: {mse_loss:.4f}")
print(f"  MAE损失: {mae_loss:.4f}")
print(f"  Smooth L1损失: {smooth_l1_loss:.4f}")

# 分类问题的损失函数
print("\n分类损失函数:")
logits = torch.tensor([[2.0, 1.0, 0.1], [0.5, 2.5, 1.0]])  # 2个样本，3个类别
targets = torch.tensor([0, 1])  # 真实类别

ce_loss = F.cross_entropy(logits, targets)
print(f"  Logits: {logits}")
print(f"  真实标签: {targets}")
print(f"  交叉熵损失: {ce_loss:.4f}")

# 7. 优化器
print("\n7. 优化器")
print("-" * 30)

# 创建一个简单的网络和数据
simple_net = nn.Linear(2, 1)
x = torch.randn(10, 2)
y = torch.randn(10, 1)

# 不同的优化器
optimizers = {
    'SGD': optim.SGD(simple_net.parameters(), lr=0.01),
    'Adam': optim.Adam(simple_net.parameters(), lr=0.01),
    'RMSprop': optim.RMSprop(simple_net.parameters(), lr=0.01),
    'AdaGrad': optim.Adagrad(simple_net.parameters(), lr=0.01)
}

print("优化器类型:")
for name, optimizer in optimizers.items():
    print(f"  {name}: {type(optimizer).__name__}")

# 8. 完整的训练循环示例
print("\n8. 完整的训练循环示例")
print("-" * 30)

def train_simple_regression():
    """简单的回归训练示例"""
    
    # 生成合成数据: y = 2x + 1 + noise
    torch.manual_seed(42)  # 设置随机种子以便复现
    n_samples = 100
    x_train = torch.randn(n_samples, 1)
    y_train = 2 * x_train + 1 + 0.1 * torch.randn(n_samples, 1)
    
    # 创建模型
    model = nn.Linear(1, 1)
    
    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.SGD(model.parameters(), lr=0.01)
    
    print("训练简单线性回归模型: y = 2x + 1")
    print(f"真实参数: weight=2.0, bias=1.0")
    
    # 训练循环
    num_epochs = 100
    for epoch in range(num_epochs):
        # 前向传播
        y_pred = model(x_train)
        loss = criterion(y_pred, y_train)
        
        # 反向传播
        optimizer.zero_grad()  # 清零梯度
        loss.backward()        # 计算梯度
        optimizer.step()       # 更新参数
        
        # 打印进度
        if (epoch + 1) % 20 == 0:
            weight = model.weight.item()
            bias = model.bias.item()
            print(f"  Epoch {epoch+1:3d}: Loss={loss.item():.4f}, "
                  f"Weight={weight:.3f}, Bias={bias:.3f}")
    
    # 最终结果
    final_weight = model.weight.item()
    final_bias = model.bias.item()
    print(f"\n训练完成!")
    print(f"学到的参数: weight={final_weight:.3f}, bias={final_bias:.3f}")

train_simple_regression()

# 9. 模型保存和加载
print("\n9. 模型保存和加载")
print("-" * 30)

# 创建一个模型
model = SimpleNet(10, 20, 3)
print("原始模型参数 (前5个):")
for name, param in list(model.named_parameters())[:2]:
    print(f"  {name}: {param.data.flatten()[:5]}")

# 保存模型
torch.save(model.state_dict(), 'model_weights.pth')
print("\n模型权重已保存到 'model_weights.pth'")

# 创建新模型并加载权重
new_model = SimpleNet(10, 20, 3)
new_model.load_state_dict(torch.load('model_weights.pth'))
print("权重已加载到新模型")

# 验证权重是否相同
print("\n新模型参数 (前5个):")
for name, param in list(new_model.named_parameters())[:2]:
    print(f"  {name}: {param.data.flatten()[:5]}")

# 10. 实践练习 - 多层感知机分类器
print("\n10. 实践练习 - 多层感知机分类器")
print("-" * 30)

class MLPClassifier(nn.Module):
    """多层感知机分类器"""
    
    def __init__(self, input_size, hidden_sizes, num_classes, dropout_rate=0.2):
        super(MLPClassifier, self).__init__()
        
        # 构建层列表
        layers = []
        prev_size = input_size
        
        # 隐藏层
        for hidden_size in hidden_sizes:
            layers.append(nn.Linear(prev_size, hidden_size))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_rate))
            prev_size = hidden_size
        
        # 输出层
        layers.append(nn.Linear(prev_size, num_classes))
        
        # 使用Sequential容器
        self.network = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.network(x)

# 创建分类器
classifier = MLPClassifier(
    input_size=4,
    hidden_sizes=[10, 8],
    num_classes=3,
    dropout_rate=0.2
)

print(f"分类器结构:\n{classifier}")

# 生成示例数据
batch_size = 32
input_data = torch.randn(batch_size, 4)
targets = torch.randint(0, 3, (batch_size,))

# 前向传播
logits = classifier(input_data)
probabilities = F.softmax(logits, dim=1)

print(f"\n示例预测:")
print(f"输入形状: {input_data.shape}")
print(f"输出logits形状: {logits.shape}")
print(f"预测概率 (前3个样本):\n{probabilities[:3]}")

# 计算损失
loss = F.cross_entropy(logits, targets)
print(f"交叉熵损失: {loss.item():.4f}")

# 计算准确率
predictions = torch.argmax(logits, dim=1)
accuracy = (predictions == targets).float().mean()
print(f"准确率: {accuracy.item():.4f}")

print("\n" + "=" * 50)
print("PyTorch神经网络学习完成！")
print("接下来请运行 08_training_example.py 学习完整的训练流程")
print("=" * 50)
