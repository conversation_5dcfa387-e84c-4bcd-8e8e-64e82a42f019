#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python数据结构学习 - 第四部分
包含：列表、元组、字典、集合、字符串等数据结构的详细操作
"""

print("=" * 50)
print("Python数据结构学习")
print("=" * 50)

# 1. 列表 (List) - 可变、有序、允许重复
print("\n1. 列表 (List)")
print("-" * 30)

# 创建列表
fruits = ["苹果", "香蕉", "橙子", "葡萄"]
numbers = [1, 2, 3, 4, 5]
mixed = ["文本", 123, True, 3.14]

print(f"水果列表: {fruits}")
print(f"数字列表: {numbers}")
print(f"混合列表: {mixed}")

# 列表索引和切片
print(f"\n列表索引操作:")
print(f"第一个水果: {fruits[0]}")
print(f"最后一个水果: {fruits[-1]}")
print(f"前两个水果: {fruits[:2]}")
print(f"后两个水果: {fruits[-2:]}")
print(f"倒序: {fruits[::-1]}")

# 列表方法
print(f"\n列表方法:")
fruits.append("草莓")  # 添加元素
print(f"添加草莓后: {fruits}")

fruits.insert(1, "芒果")  # 在指定位置插入
print(f"在位置1插入芒果: {fruits}")

removed = fruits.pop()  # 删除并返回最后一个元素
print(f"删除最后一个元素 '{removed}': {fruits}")

fruits.remove("香蕉")  # 删除指定元素
print(f"删除香蕉: {fruits}")

# 列表排序
numbers = [3, 1, 4, 1, 5, 9, 2, 6]
print(f"\n原数字列表: {numbers}")
numbers.sort()  # 原地排序
print(f"升序排序: {numbers}")

numbers.sort(reverse=True)  # 降序排序
print(f"降序排序: {numbers}")

# 列表推导式
squares = [x**2 for x in range(1, 6)]
even_squares = [x**2 for x in range(1, 11) if x % 2 == 0]
print(f"\n列表推导式:")
print(f"1-5的平方: {squares}")
print(f"1-10中偶数的平方: {even_squares}")

# 2. 元组 (Tuple) - 不可变、有序、允许重复
print("\n2. 元组 (Tuple)")
print("-" * 30)

# 创建元组
coordinates = (10, 20)
colors = ("红", "绿", "蓝", "红")  # 允许重复
single_tuple = (42,)  # 单元素元组需要逗号

print(f"坐标: {coordinates}")
print(f"颜色: {colors}")
print(f"单元素元组: {single_tuple}")

# 元组解包
x, y = coordinates
print(f"解包坐标: x={x}, y={y}")

# 元组作为字典的键（因为不可变）
locations = {
    (0, 0): "原点",
    (1, 1): "右上",
    (-1, -1): "左下"
}
print(f"位置字典: {locations}")

# 命名元组
from collections import namedtuple

Point = namedtuple('Point', ['x', 'y'])
p1 = Point(3, 4)
print(f"命名元组: {p1}")
print(f"访问属性: x={p1.x}, y={p1.y}")

# 3. 字典 (Dictionary) - 可变、无序（Python 3.7+保持插入顺序）、键唯一
print("\n3. 字典 (Dictionary)")
print("-" * 30)

# 创建字典
student = {
    "姓名": "小明",
    "年龄": 18,
    "成绩": [85, 92, 78],
    "是否在校": True
}

print(f"学生信息: {student}")

# 字典操作
print(f"\n字典操作:")
print(f"姓名: {student['姓名']}")
print(f"年龄: {student.get('年龄', '未知')}")  # 安全获取

student["专业"] = "计算机科学"  # 添加新键值对
print(f"添加专业后: {student}")

student["年龄"] = 19  # 修改值
print(f"修改年龄后: {student['年龄']}")

# 字典方法
print(f"\n字典方法:")
print(f"所有键: {list(student.keys())}")
print(f"所有值: {list(student.values())}")
print(f"所有键值对: {list(student.items())}")

# 字典推导式
numbers = [1, 2, 3, 4, 5]
square_dict = {x: x**2 for x in numbers}
print(f"字典推导式: {square_dict}")

# 嵌套字典
class_info = {
    "班级": "Python101",
    "学生": {
        "小明": {"年龄": 18, "成绩": 85},
        "小红": {"年龄": 19, "成绩": 92}
    }
}
print(f"嵌套字典: {class_info['学生']['小明']}")

# 4. 集合 (Set) - 可变、无序、元素唯一
print("\n4. 集合 (Set)")
print("-" * 30)

# 创建集合
fruits_set = {"苹果", "香蕉", "橙子", "苹果"}  # 重复元素会被去除
numbers_set = set([1, 2, 3, 2, 1])

print(f"水果集合: {fruits_set}")
print(f"数字集合: {numbers_set}")

# 集合操作
set1 = {1, 2, 3, 4, 5}
set2 = {4, 5, 6, 7, 8}

print(f"\n集合运算:")
print(f"集合1: {set1}")
print(f"集合2: {set2}")
print(f"并集: {set1 | set2}")
print(f"交集: {set1 & set2}")
print(f"差集: {set1 - set2}")
print(f"对称差集: {set1 ^ set2}")

# 集合方法
fruits_set.add("葡萄")
print(f"添加葡萄: {fruits_set}")

fruits_set.remove("香蕉")
print(f"删除香蕉: {fruits_set}")

# 5. 字符串 (String) - 不可变、有序
print("\n5. 字符串详细操作")
print("-" * 30)

text = "Hello Python World"
print(f"原字符串: '{text}'")

# 字符串方法
print(f"\n字符串方法:")
print(f"长度: {len(text)}")
print(f"大写: {text.upper()}")
print(f"小写: {text.lower()}")
print(f"首字母大写: {text.title()}")
print(f"是否以Hello开头: {text.startswith('Hello')}")
print(f"是否以World结尾: {text.endswith('World')}")
print(f"查找Python位置: {text.find('Python')}")
print(f"替换Python为Java: {text.replace('Python', 'Java')}")

# 字符串分割和连接
words = text.split()
print(f"分割成单词: {words}")
joined = "-".join(words)
print(f"用-连接: {joined}")

# 字符串格式化
name = "小明"
age = 18
score = 85.5

# 方法1: f-string (推荐)
message1 = f"学生{name}今年{age}岁，成绩是{score:.1f}分"

# 方法2: format方法
message2 = "学生{}今年{}岁，成绩是{:.1f}分".format(name, age, score)

# 方法3: % 格式化
message3 = "学生%s今年%d岁，成绩是%.1f分" % (name, age, score)

print(f"\n字符串格式化:")
print(f"f-string: {message1}")
print(f"format: {message2}")
print(f"% 格式化: {message3}")

# 6. 数据结构转换
print("\n6. 数据结构转换")
print("-" * 30)

# 列表转换
original_list = [1, 2, 3, 2, 1]
print(f"原列表: {original_list}")
print(f"转元组: {tuple(original_list)}")
print(f"转集合: {set(original_list)}")  # 去重

# 字符串转换
text = "hello"
print(f"字符串: {text}")
print(f"转列表: {list(text)}")
print(f"转集合: {set(text)}")

# 字典转换
student_dict = {"name": "小明", "age": 18}
print(f"字典: {student_dict}")
print(f"键转列表: {list(student_dict.keys())}")
print(f"值转列表: {list(student_dict.values())}")

# 7. 实践练习 - 学生成绩管理系统
print("\n7. 实践练习 - 学生成绩管理系统")
print("-" * 30)

class GradeManager:
    """成绩管理系统"""
    
    def __init__(self):
        # 使用字典存储学生信息，列表存储成绩
        self.students = {}
    
    def add_student(self, name, student_id):
        """添加学生"""
        self.students[student_id] = {
            "姓名": name,
            "成绩": [],
            "科目": set()  # 使用集合存储科目，自动去重
        }
        print(f"  添加学生: {name} (学号: {student_id})")
    
    def add_grade(self, student_id, subject, score):
        """添加成绩"""
        if student_id in self.students:
            self.students[student_id]["成绩"].append((subject, score))
            self.students[student_id]["科目"].add(subject)
            print(f"  {self.students[student_id]['姓名']}的{subject}成绩{score}分已添加")
        else:
            print(f"  学号{student_id}不存在")
    
    def get_average(self, student_id):
        """计算学生平均分"""
        if student_id in self.students:
            grades = self.students[student_id]["成绩"]
            if grades:
                total = sum(score for _, score in grades)
                return total / len(grades)
        return 0
    
    def get_subject_average(self, subject):
        """计算某科目的班级平均分"""
        scores = []
        for student in self.students.values():
            for subj, score in student["成绩"]:
                if subj == subject:
                    scores.append(score)
        return sum(scores) / len(scores) if scores else 0
    
    def get_top_students(self, n=3):
        """获取前n名学生"""
        student_averages = []
        for student_id, info in self.students.items():
            avg = self.get_average(student_id)
            student_averages.append((info["姓名"], avg, student_id))
        
        # 按平均分降序排序
        student_averages.sort(key=lambda x: x[1], reverse=True)
        return student_averages[:n]

# 使用成绩管理系统
manager = GradeManager()

# 添加学生
manager.add_student("小明", "2023001")
manager.add_student("小红", "2023002")
manager.add_student("小李", "2023003")

# 添加成绩
manager.add_grade("2023001", "数学", 85)
manager.add_grade("2023001", "英语", 92)
manager.add_grade("2023002", "数学", 78)
manager.add_grade("2023002", "英语", 88)
manager.add_grade("2023003", "数学", 95)
manager.add_grade("2023003", "英语", 85)

# 查询结果
print(f"\n查询结果:")
for student_id in ["2023001", "2023002", "2023003"]:
    name = manager.students[student_id]["姓名"]
    avg = manager.get_average(student_id)
    subjects = manager.students[student_id]["科目"]
    print(f"  {name}: 平均分{avg:.1f}, 科目{list(subjects)}")

print(f"\n数学班级平均分: {manager.get_subject_average('数学'):.1f}")
print(f"英语班级平均分: {manager.get_subject_average('英语'):.1f}")

print(f"\n前3名学生:")
top_students = manager.get_top_students(3)
for i, (name, avg, student_id) in enumerate(top_students, 1):
    print(f"  第{i}名: {name} (平均分: {avg:.1f})")

print("\n" + "=" * 50)
print("Python数据结构学习完成！")
print("接下来请运行 05_pytorch_basics.py 开始PyTorch学习")
print("=" * 50)
