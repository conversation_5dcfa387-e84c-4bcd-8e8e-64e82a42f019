#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python控制结构学习 - 第二部分
包含：条件语句、循环语句、异常处理等
"""

print("=" * 50)
print("Python控制结构学习")
print("=" * 50)

# 1. 条件语句 (if-elif-else)
print("\n1. 条件语句")
print("-" * 30)

# 简单if语句
score = 85
if score >= 90:
    grade = "优秀"
elif score >= 80:
    grade = "良好"
elif score >= 70:
    grade = "中等"
elif score >= 60:
    grade = "及格"
else:
    grade = "不及格"

print(f"分数: {score}, 等级: {grade}")

# 多条件判断
age = 18
has_license = True

if age >= 18 and has_license:
    print("可以开车")
elif age >= 18 and not has_license:
    print("年龄够了，但需要考驾照")
else:
    print("年龄不够，不能开车")

# 三元运算符（条件表达式）
temperature = 25
weather = "热" if temperature > 30 else "冷" if temperature < 10 else "适中"
print(f"温度: {temperature}°C, 天气: {weather}")

# 2. for循环
print("\n2. for循环")
print("-" * 30)

# 遍历数字范围
print("数字1到5:")
for i in range(1, 6):
    print(f"  数字: {i}")

# 遍历列表
fruits = ["苹果", "香蕉", "橙子", "葡萄"]
print("\n水果列表:")
for fruit in fruits:
    print(f"  我喜欢吃{fruit}")


# 带索引的遍历
print("\n带索引的水果列表:")
for index, fruit in enumerate(fruits):
    print(f"  第{index + 1}个水果: {fruit}")

# 遍历字典
student_scores = {"小明": 85, "小红": 92, "小李": 78}
print("\n学生成绩:")
for name, score in student_scores.items():
    print(f"  {name}: {score}分")

# 列表推导式（高级for循环用法）
squares = [x**2 for x in range(1, 6)]
print(f"\n1到5的平方: {squares}")

even_squares = [x**2 for x in range(1, 11) if x % 2 == 0]
print(f"1到10中偶数的平方: {even_squares}")

# 3. while循环
print("\n3. while循环")
print("-" * 30)

# 基本while循环
count = 1
print("while循环计数:")
while count <= 5:
    print(f"  计数: {count}")
    count += 1

# 用户输入验证（模拟）
print("\n密码验证模拟:")
password = "123456"
attempts = 0
max_attempts = 3

while attempts < max_attempts:
    # 在实际程序中，这里会是: user_input = input("请输入密码: ")
    user_input = "wrong" if attempts < 2 else "123456"  # 模拟输入
    print(f"  尝试输入密码: {user_input}")
    
    if user_input == password:
        print("  密码正确！登录成功")
        break
    else:
        attempts += 1
        remaining = max_attempts - attempts
        if remaining > 0:
            print(f"  密码错误，还有{remaining}次机会")
        else:
            print("  密码错误次数过多，账户被锁定")

# 4. 循环控制语句
print("\n4. 循环控制语句")
print("-" * 30)

# break语句 - 跳出循环
print("break示例 - 找到第一个偶数:")
numbers = [1, 3, 5, 8, 9, 10]
for num in numbers:
    print(f"  检查数字: {num}")
    if num % 2 == 0:
        print(f"  找到第一个偶数: {num}")
        break

# continue语句 - 跳过当前迭代
print("\ncontinue示例 - 只打印偶数:")
for num in range(1, 11):
    if num % 2 != 0:  # 如果是奇数
        continue      # 跳过这次循环
    print(f"  偶数: {num}")

# 5. 嵌套循环
print("\n5. 嵌套循环")
print("-" * 30)

# 打印乘法表
print("3x3乘法表:")
for i in range(1, 4):
    for j in range(1, 4):
        result = i * j
        print(f"{i}x{j}={result}", end="  ")
    print()  # 换行

# 6. 异常处理
print("\n6. 异常处理")
print("-" * 30)

# 基本异常处理
print("除法运算异常处理:")
numbers = [10, 5, 0, 2]
for num in numbers:
    try:
        result = 20 / num
        print(f"  20 / {num} = {result}")
    except ZeroDivisionError:
        print(f"  错误: 不能除以{num}（除零错误）")
    except Exception as e:
        print(f"  发生其他错误: {e}")

try:
    result = 10 / 0
except ZeroDivisionError:
    print("  除零错误")

# 多种异常类型
print("\n类型转换异常处理:")
test_values = ["123", "abc", "45.6", ""]
for value in test_values:
    try:
        number = int(value)
        print(f"  '{value}' 转换为整数: {number}")
    except ValueError:
        print(f"  '{value}' 无法转换为整数")
    except Exception as e:
        print(f"  处理 '{value}' 时发生错误: {e}")

# try-except-else-finally
print("\n完整异常处理结构:")
try:
    result = 10 / 2
    print(f"  计算结果: {result}")
except ZeroDivisionError:
    print("  除零错误")
else:
    print("  没有发生异常，计算成功")
finally:
    print("  无论是否有异常，这里都会执行")

# 7. 实践练习
print("\n7. 实践练习")
print("-" * 30)

# 练习1: 猜数字游戏（简化版）
import random

target = random.randint(1, 10)
guesses = [3, 7, target]  # 模拟用户猜测

print(f"猜数字游戏（目标数字是1-10之间）:")
print(f"（提示：目标数字是 {target}）")

for i, guess in enumerate(guesses, 1):
    print(f"  第{i}次猜测: {guess}")
    if guess == target:
        print(f"  恭喜！猜对了，数字就是 {target}")
        break
    elif guess < target:
        print("  太小了，再试试")
    else:
        print("  太大了，再试试")

# 练习2: 统计字符
text = "Hello Python World"
char_count = {}
print(f"\n统计字符串 '{text}' 中每个字符的出现次数:")

for char in text.lower():
    if char.isalpha():  # 只统计字母
        char_count[char] = char_count.get(char, 0) + 1

for char, count in sorted(char_count.items()):
    print(f"  '{char}': {count}次")

print("\n" + "=" * 50)
print("Python控制结构学习完成！")
print("接下来请运行 03_functions_classes.py")
print("=" * 50)
