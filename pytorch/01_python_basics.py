#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python基础语法学习 - 第一部分
包含：变量、数据类型、运算符、字符串操作等基础概念
"""

print("=" * 50)
print("欢迎来到Python基础语法学习！")
print("=" * 50)

# 1. 变量和基本数据类型
print("\n1. 变量和基本数据类型")
print("-" * 30)

# 整数 (int)
age = 25
print(f"年龄: {age}, 类型: {type(age)}")

# 浮点数 (float)
height = 175.5
print(f"身高: {height}cm, 类型: {type(height)}")

# 字符串 (str)
name = "小明"
print(f"姓名: {name}, 类型: {type(name)}")

# 布尔值 (bool)
is_student = True
print(f"是学生: {is_student}, 类型: {type(is_student)}")

# 空值 (NoneType)
nothing = None
print(f"空值: {nothing}, 类型: {type(nothing)}")

# 2. 字符串操作
print("\n2. 字符串操作")
print("-" * 30)

# 字符串拼接
first_name = "张"
last_name = "三"
full_name = first_name + last_name
print(f"姓名拼接: {full_name}")

# 字符串格式化
score = 95.5
message = f"学生{full_name}的成绩是{score}分"
print(f"格式化字符串: {message}")

# 字符串方法
text = "  Hello Python World  "
print(f"原始字符串: '{text}'")
print(f"去除空格: '{text.strip()}'")
print(f"转大写: '{text.upper()}'")
print(f"转小写: '{text.lower()}'")
print(f"替换: '{text.replace('Python', 'Java')}'")
print(f"分割: {text.strip().split(' ')}")

# 3. 数学运算
print("\n3. 数学运算")
print("-" * 30)

a = 10
b = 3

print(f"a = {a}, b = {b}")
print(f"加法: {a} + {b} = {a + b}")
print(f"减法: {a} - {b} = {a - b}")
print(f"乘法: {a} * {b} = {a * b}")
print(f"除法: {a} / {b} = {a / b}")
print(f"整除: {a} // {b} = {a // b}")
print(f"取余: {a} % {b} = {a % b}")
print(f"幂运算: {a} ** {b} = {a ** b}")

# 4. 比较运算符
print("\n4. 比较运算符")
print("-" * 30)

x = 5
y = 8

print(f"x = {x}, y = {y}")
print(f"x == y: {x == y}")  # 等于
print(f"x != y: {x != y}")  # 不等于
print(f"x > y: {x > y}")    # 大于
print(f"x < y: {x < y}")    # 小于
print(f"x >= y: {x >= y}")  # 大于等于
print(f"x <= y: {x <= y}")  # 小于等于

# 5. 逻辑运算符
print("\n5. 逻辑运算符")
print("-" * 30)

p = True
q = False

print(f"p = {p}, q = {q}")
print(f"p and q: {p and q}")  # 与
print(f"p or q: {p or q}")    # 或
print(f"not p: {not p}")      # 非

# 6. 输入输出
print("\n6. 输入输出示例")
print("-" * 30)

# 注意：在实际运行时可以取消注释下面的代码来测试输入
# user_name = input("请输入你的姓名: ")
# user_age = int(input("请输入你的年龄: "))
# print(f"你好 {user_name}，你今年 {user_age} 岁")

# 模拟输入输出
print("模拟输入输出:")
user_name = "学习者"
user_age = 20
print(f"你好 {user_name}，你今年 {user_age} 岁")

# 7. 类型转换
print("\n7. 类型转换")
print("-" * 30)

# 字符串转数字
str_num = "123"
int_num = int(str_num)
float_num = float(str_num)
print(f"字符串 '{str_num}' 转整数: {int_num}")
print(f"字符串 '{str_num}' 转浮点数: {float_num}")

# 数字转字符串
num = 456
str_from_num = str(num)
print(f"数字 {num} 转字符串: '{str_from_num}'")

# 布尔值转换
print(f"bool(1): {bool(1)}")      # True
print(f"bool(0): {bool(0)}")      # False
print(f"bool(''): {bool('')}")    # False
print(f"bool('hello'): {bool('hello')}")  # True

print("\n" + "=" * 50)
print("Python基础语法第一部分学习完成！")
print("接下来请运行 02_control_structures.py")
print("=" * 50)
