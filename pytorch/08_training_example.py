#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTorch完整训练示例 - 第八部分
包含：数据加载、模型训练、验证、可视化等完整流程
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, TensorDataset
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification, make_regression
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import time

print("=" * 50)
print("PyTorch完整训练示例")
print("=" * 50)

# 设置随机种子以便复现结果
torch.manual_seed(42)
np.random.seed(42)

# 1. 数据准备和加载
print("\n1. 数据准备和加载")
print("-" * 30)

class CustomDataset(Dataset):
    """自定义数据集类"""
    
    def __init__(self, X, y, transform=None):
        self.X = torch.FloatTensor(X)
        self.y = torch.LongTensor(y) if y.dtype == np.int64 else torch.FloatTensor(y)
        self.transform = transform
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        sample = self.X[idx]
        target = self.y[idx]
        
        if self.transform:
            sample = self.transform(sample)
        
        return sample, target

# 生成分类数据
print("生成分类数据集...")
X_class, y_class = make_classification(
    n_samples=1000,
    n_features=20,
    n_informative=10,
    n_redundant=10,
    n_classes=3,
    random_state=42
)

# 数据标准化
scaler = StandardScaler()
X_class_scaled = scaler.fit_transform(X_class)

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(
    X_class_scaled, y_class, test_size=0.2, random_state=42
)

print(f"训练集大小: {X_train.shape}")
print(f"测试集大小: {X_test.shape}")
print(f"特征数量: {X_train.shape[1]}")
print(f"类别数量: {len(np.unique(y_class))}")

# 创建数据集和数据加载器
train_dataset = CustomDataset(X_train, y_train)
test_dataset = CustomDataset(X_test, y_test)

batch_size = 32
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

print(f"训练批次数量: {len(train_loader)}")
print(f"测试批次数量: {len(test_loader)}")

# 2. 模型定义
print("\n2. 模型定义")
print("-" * 30)

class ImprovedClassifier(nn.Module):
    """改进的分类器，包含批归一化和dropout"""
    
    def __init__(self, input_size, hidden_sizes, num_classes, dropout_rate=0.3):
        super(ImprovedClassifier, self).__init__()
        
        self.layers = nn.ModuleList()
        prev_size = input_size
        
        # 构建隐藏层
        for hidden_size in hidden_sizes:
            # 线性层
            self.layers.append(nn.Linear(prev_size, hidden_size))
            # 批归一化
            self.layers.append(nn.BatchNorm1d(hidden_size))
            # 激活函数
            self.layers.append(nn.ReLU())
            # Dropout
            self.layers.append(nn.Dropout(dropout_rate))
            prev_size = hidden_size
        
        # 输出层
        self.output_layer = nn.Linear(prev_size, num_classes)
    
    def forward(self, x):
        for layer in self.layers:
            x = layer(x)
        x = self.output_layer(x)
        return x

# 创建模型
model = ImprovedClassifier(
    input_size=X_train.shape[1],
    hidden_sizes=[64, 32, 16],
    num_classes=3,
    dropout_rate=0.3
)

print(f"模型结构:\n{model}")

# 计算模型参数数量
total_params = sum(p.numel() for p in model.parameters())
trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
print(f"\n模型参数:")
print(f"总参数数量: {total_params:,}")
print(f"可训练参数数量: {trainable_params:,}")

# 3. 训练配置
print("\n3. 训练配置")
print("-" * 30)

# 损失函数和优化器
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)

# 学习率调度器
scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.1)

# 设备配置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model.to(device)
print(f"使用设备: {device}")

# 4. 训练和验证函数
print("\n4. 训练和验证函数")
print("-" * 30)

def train_epoch(model, train_loader, criterion, optimizer, device):
    """训练一个epoch"""
    model.train()  # 设置为训练模式
    total_loss = 0
    correct = 0
    total = 0
    
    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)
        
        # 清零梯度
        optimizer.zero_grad()
        
        # 前向传播
        output = model(data)
        loss = criterion(output, target)
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        pred = output.argmax(dim=1)
        correct += pred.eq(target).sum().item()
        total += target.size(0)
    
    avg_loss = total_loss / len(train_loader)
    accuracy = 100. * correct / total
    return avg_loss, accuracy

def validate_epoch(model, test_loader, criterion, device):
    """验证一个epoch"""
    model.eval()  # 设置为评估模式
    total_loss = 0
    correct = 0
    total = 0
    
    with torch.no_grad():  # 不计算梯度
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            
            output = model(data)
            loss = criterion(output, target)
            
            total_loss += loss.item()
            pred = output.argmax(dim=1)
            correct += pred.eq(target).sum().item()
            total += target.size(0)
    
    avg_loss = total_loss / len(test_loader)
    accuracy = 100. * correct / total
    return avg_loss, accuracy

# 5. 训练循环
print("\n5. 开始训练")
print("-" * 30)

num_epochs = 50
train_losses = []
train_accuracies = []
val_losses = []
val_accuracies = []

best_val_accuracy = 0
best_model_state = None

print("Epoch | Train Loss | Train Acc | Val Loss | Val Acc | LR")
print("-" * 60)

start_time = time.time()

for epoch in range(num_epochs):
    # 训练
    train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)
    
    # 验证
    val_loss, val_acc = validate_epoch(model, test_loader, criterion, device)
    
    # 更新学习率
    scheduler.step()
    
    # 记录历史
    train_losses.append(train_loss)
    train_accuracies.append(train_acc)
    val_losses.append(val_loss)
    val_accuracies.append(val_acc)
    
    # 保存最佳模型
    if val_acc > best_val_accuracy:
        best_val_accuracy = val_acc
        best_model_state = model.state_dict().copy()
    
    # 打印进度
    current_lr = optimizer.param_groups[0]['lr']
    if (epoch + 1) % 10 == 0 or epoch < 5:
        print(f"{epoch+1:5d} | {train_loss:10.4f} | {train_acc:9.2f} | "
              f"{val_loss:8.4f} | {val_acc:7.2f} | {current_lr:.2e}")

training_time = time.time() - start_time
print(f"\n训练完成! 用时: {training_time:.2f}秒")
print(f"最佳验证准确率: {best_val_accuracy:.2f}%")

# 6. 结果可视化
print("\n6. 结果可视化")
print("-" * 30)

def plot_training_history():
    """绘制训练历史"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # 损失曲线
    ax1.plot(train_losses, label='训练损失', color='blue')
    ax1.plot(val_losses, label='验证损失', color='red')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('损失')
    ax1.set_title('训练和验证损失')
    ax1.legend()
    ax1.grid(True)
    
    # 准确率曲线
    ax2.plot(train_accuracies, label='训练准确率', color='blue')
    ax2.plot(val_accuracies, label='验证准确率', color='red')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('准确率 (%)')
    ax2.set_title('训练和验证准确率')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('training_history.png', dpi=150, bbox_inches='tight')
    print("训练历史图已保存为 'training_history.png'")
    
    # 显示最终结果
    print(f"\n最终结果:")
    print(f"训练损失: {train_losses[-1]:.4f}")
    print(f"训练准确率: {train_accuracies[-1]:.2f}%")
    print(f"验证损失: {val_losses[-1]:.4f}")
    print(f"验证准确率: {val_accuracies[-1]:.2f}%")

try:
    plot_training_history()
except ImportError:
    print("matplotlib未安装，跳过可视化")
    print(f"最终训练准确率: {train_accuracies[-1]:.2f}%")
    print(f"最终验证准确率: {val_accuracies[-1]:.2f}%")

# 7. 模型评估
print("\n7. 详细模型评估")
print("-" * 30)

# 加载最佳模型
model.load_state_dict(best_model_state)

def detailed_evaluation(model, test_loader, device):
    """详细评估模型性能"""
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            pred = output.argmax(dim=1)
            
            all_predictions.extend(pred.cpu().numpy())
            all_targets.extend(target.cpu().numpy())
    
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    
    # 计算每个类别的准确率
    num_classes = len(np.unique(all_targets))
    class_accuracies = []
    
    print("各类别准确率:")
    for class_idx in range(num_classes):
        class_mask = all_targets == class_idx
        if np.sum(class_mask) > 0:
            class_acc = np.mean(all_predictions[class_mask] == all_targets[class_mask])
            class_accuracies.append(class_acc)
            print(f"  类别 {class_idx}: {class_acc:.4f} ({class_acc*100:.2f}%)")
    
    # 总体准确率
    overall_accuracy = np.mean(all_predictions == all_targets)
    print(f"\n总体准确率: {overall_accuracy:.4f} ({overall_accuracy*100:.2f}%)")
    
    return all_predictions, all_targets

predictions, targets = detailed_evaluation(model, test_loader, device)

# 8. 模型保存
print("\n8. 模型保存")
print("-" * 30)

# 保存完整模型
torch.save({
    'model_state_dict': best_model_state,
    'optimizer_state_dict': optimizer.state_dict(),
    'best_val_accuracy': best_val_accuracy,
    'model_config': {
        'input_size': X_train.shape[1],
        'hidden_sizes': [64, 32, 16],
        'num_classes': 3,
        'dropout_rate': 0.3
    }
}, 'best_model.pth')

print("最佳模型已保存为 'best_model.pth'")

# 9. 模型推理示例
print("\n9. 模型推理示例")
print("-" * 30)

def predict_sample(model, sample, device):
    """对单个样本进行预测"""
    model.eval()
    with torch.no_grad():
        if len(sample.shape) == 1:
            sample = sample.unsqueeze(0)  # 添加batch维度
        
        sample = sample.to(device)
        output = model(sample)
        probabilities = F.softmax(output, dim=1)
        predicted_class = output.argmax(dim=1)
        
        return predicted_class.item(), probabilities.squeeze().cpu().numpy()

# 随机选择几个测试样本进行预测
test_indices = np.random.choice(len(X_test), 5, replace=False)

print("随机样本预测结果:")
for i, idx in enumerate(test_indices):
    sample = torch.FloatTensor(X_test[idx])
    true_label = y_test[idx]
    
    pred_class, probabilities = predict_sample(model, sample, device)
    
    print(f"\n样本 {i+1}:")
    print(f"  真实标签: {true_label}")
    print(f"  预测标签: {pred_class}")
    print(f"  预测概率: {probabilities}")
    print(f"  预测正确: {'✓' if pred_class == true_label else '✗'}")

# 10. 总结
print("\n10. 训练总结")
print("-" * 30)
print(f"""
训练配置:
- 数据集大小: {len(X_train)} (训练) + {len(X_test)} (测试)
- 特征维度: {X_train.shape[1]}
- 类别数量: {len(np.unique(y_class))}
- 批次大小: {batch_size}
- 训练轮数: {num_epochs}
- 优化器: Adam
- 学习率: 0.001
- 设备: {device}

最终结果:
- 最佳验证准确率: {best_val_accuracy:.2f}%
- 训练时间: {training_time:.2f}秒
- 模型参数数量: {total_params:,}

文件输出:
- 最佳模型: best_model.pth
- 训练历史图: training_history.png (如果matplotlib可用)
""")

print("\n" + "=" * 50)
print("PyTorch完整训练示例学习完成！")
print("恭喜你完成了Python和PyTorch的基础学习！")
print("=" * 50)
