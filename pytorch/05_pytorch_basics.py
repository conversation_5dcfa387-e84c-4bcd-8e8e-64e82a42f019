#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTorch基础学习 - 第五部分
包含：PyTorch简介、安装、基本概念、张量创建等
"""

print("=" * 50)
print("PyTorch基础学习")
print("=" * 50)

# 首先检查PyTorch是否安装
try:
    import torch
    import numpy as np
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA是否可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
except ImportError:
    print("PyTorch未安装，请运行: pip install torch torchvision")
    print("如果需要GPU支持，请访问 https://pytorch.org 获取安装命令")
    exit(1)

# 1. PyTorch简介
print("\n1. PyTorch简介")
print("-" * 30)
print("""
PyTorch是一个开源的机器学习框架，主要特点：
- 动态计算图：可以在运行时改变网络结构
- 自动微分：自动计算梯度
- GPU加速：支持CUDA进行GPU计算
- Pythonic：符合Python编程习惯
- 丰富的生态：包含计算机视觉、自然语言处理等工具
""")

# 2. 张量 (Tensor) 基础
print("\n2. 张量 (Tensor) 基础")
print("-" * 30)

# 张量是PyTorch中的基本数据结构，类似于NumPy的数组
print("张量创建方法:")

# 从Python列表创建
list_data = [1, 2, 3, 4]
tensor_from_list = torch.tensor(list_data)
print(f"从列表创建: {tensor_from_list}")

# 从NumPy数组创建
numpy_array = np.array([1, 2, 3, 4])
tensor_from_numpy = torch.from_numpy(numpy_array)
print(f"从NumPy创建: {tensor_from_numpy}")

# 创建特殊张量
zeros_tensor = torch.zeros(2, 3)  # 全零张量
ones_tensor = torch.ones(2, 3)    # 全一张量
random_tensor = torch.randn(2, 3) # 随机张量（标准正态分布）
range_tensor = torch.arange(0, 10, 2)  # 范围张量

print(f"\n特殊张量:")
print(f"全零张量 (2x3):\n{zeros_tensor}")
print(f"全一张量 (2x3):\n{ones_tensor}")
print(f"随机张量 (2x3):\n{random_tensor}")
print(f"范围张量 (0到10，步长2): {range_tensor}")

# 3. 张量属性
print("\n3. 张量属性")
print("-" * 30)

sample_tensor = torch.randn(3, 4, 5)
print(f"张量: {sample_tensor.shape}")
print(f"形状 (shape): {sample_tensor.shape}")
print(f"尺寸 (size): {sample_tensor.size()}")
print(f"维度数 (ndim): {sample_tensor.ndim}")
print(f"元素总数: {sample_tensor.numel()}")
print(f"数据类型: {sample_tensor.dtype}")
print(f"设备: {sample_tensor.device}")

# 4. 张量数据类型
print("\n4. 张量数据类型")
print("-" * 30)

# 不同数据类型的张量
int_tensor = torch.tensor([1, 2, 3], dtype=torch.int32)
float_tensor = torch.tensor([1.0, 2.0, 3.0], dtype=torch.float32)
double_tensor = torch.tensor([1.0, 2.0, 3.0], dtype=torch.float64)
bool_tensor = torch.tensor([True, False, True], dtype=torch.bool)

print(f"整数张量: {int_tensor}, 类型: {int_tensor.dtype}")
print(f"浮点张量: {float_tensor}, 类型: {float_tensor.dtype}")
print(f"双精度张量: {double_tensor}, 类型: {double_tensor.dtype}")
print(f"布尔张量: {bool_tensor}, 类型: {bool_tensor.dtype}")

# 类型转换
converted_tensor = int_tensor.float()
print(f"类型转换: {converted_tensor}, 类型: {converted_tensor.dtype}")

# 5. 张量形状操作
print("\n5. 张量形状操作")
print("-" * 30)

# 创建一个示例张量
x = torch.arange(12)
print(f"原始张量: {x}")
print(f"形状: {x.shape}")

# 重塑张量
reshaped = x.view(3, 4)  # 重塑为3x4
print(f"\n重塑为3x4:\n{reshaped}")

reshaped2 = x.view(2, 6)  # 重塑为2x6
print(f"\n重塑为2x6:\n{reshaped2}")

reshaped3 = x.view(-1, 3)  # -1表示自动计算该维度
print(f"\n重塑为?x3:\n{reshaped3}")

# 添加和删除维度
unsqueezed = x.unsqueeze(0)  # 在第0维添加维度
print(f"\n添加维度: {unsqueezed.shape}")

squeezed = unsqueezed.squeeze(0)  # 删除第0维
print(f"删除维度: {squeezed.shape}")

# 转置
matrix = torch.randn(3, 4)
transposed = matrix.t()  # 转置
print(f"\n原矩阵形状: {matrix.shape}")
print(f"转置后形状: {transposed.shape}")

# 6. 张量索引和切片
print("\n6. 张量索引和切片")
print("-" * 30)

# 创建一个2D张量
matrix = torch.arange(12).view(3, 4)
print(f"原矩阵:\n{matrix}")

# 索引操作
print(f"\n索引操作:")
print(f"第一行: {matrix[0]}")
print(f"第一列: {matrix[:, 0]}")
print(f"第二行第三列: {matrix[1, 2]}")
print(f"前两行: \n{matrix[:2]}")
print(f"后两列: \n{matrix[:, -2:]}")

# 布尔索引
mask = matrix > 5
print(f"\n布尔掩码 (>5):\n{mask}")
print(f"大于5的元素: {matrix[mask]}")

# 7. 设备管理 (CPU vs GPU)
print("\n7. 设备管理")
print("-" * 30)

# 检查设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 创建张量并移动到设备
cpu_tensor = torch.randn(3, 3)
print(f"CPU张量设备: {cpu_tensor.device}")

# 如果有GPU，移动到GPU
if torch.cuda.is_available():
    gpu_tensor = cpu_tensor.to(device)
    print(f"GPU张量设备: {gpu_tensor.device}")
    
    # 从GPU移回CPU
    back_to_cpu = gpu_tensor.cpu()
    print(f"移回CPU设备: {back_to_cpu.device}")
else:
    print("没有可用的GPU，继续使用CPU")

# 8. 张量与NumPy的互操作
print("\n8. 张量与NumPy的互操作")
print("-" * 30)

# PyTorch张量转NumPy数组
torch_tensor = torch.randn(2, 3)
numpy_array = torch_tensor.numpy()
print(f"PyTorch张量:\n{torch_tensor}")
print(f"转换为NumPy数组:\n{numpy_array}")

# NumPy数组转PyTorch张量
numpy_data = np.array([[1, 2, 3], [4, 5, 6]])
torch_from_numpy = torch.from_numpy(numpy_data)
print(f"\nNumPy数组:\n{numpy_data}")
print(f"转换为PyTorch张量:\n{torch_from_numpy}")

# 注意：共享内存
print(f"\n内存共享测试:")
print(f"修改前 - PyTorch: {torch_tensor[0, 0]}, NumPy: {numpy_array[0, 0]}")
torch_tensor[0, 0] = 999
print(f"修改后 - PyTorch: {torch_tensor[0, 0]}, NumPy: {numpy_array[0, 0]}")
print("注意：CPU张量和NumPy数组共享内存！")

# 9. 实践练习 - 创建和操作张量
print("\n9. 实践练习")
print("-" * 30)

def tensor_practice():
    """张量操作练习"""
    print("练习1: 创建一个5x5的单位矩阵")
    identity = torch.eye(5)
    print(f"单位矩阵:\n{identity}")
    
    print("\n练习2: 创建一个3x4的随机矩阵，然后计算每行的和")
    random_matrix = torch.randn(3, 4)
    row_sums = random_matrix.sum(dim=1)  # dim=1表示按行求和
    print(f"随机矩阵:\n{random_matrix}")
    print(f"每行的和: {row_sums}")
    
    print("\n练习3: 创建两个矩阵并进行元素级乘法")
    a = torch.ones(2, 3)
    b = torch.arange(6).view(2, 3)
    element_wise_product = a * b
    print(f"矩阵A:\n{a}")
    print(f"矩阵B:\n{b}")
    print(f"元素级乘法:\n{element_wise_product}")
    
    print("\n练习4: 使用条件创建张量")
    x = torch.randn(3, 3)
    # 将负数替换为0
    x_positive = torch.where(x > 0, x, torch.zeros_like(x))
    print(f"原张量:\n{x}")
    print(f"负数替换为0:\n{x_positive}")

tensor_practice()

# 10. 常用张量创建函数总结
print("\n10. 常用张量创建函数总结")
print("-" * 30)

creation_examples = {
    "torch.zeros(2, 3)": torch.zeros(2, 3),
    "torch.ones(2, 3)": torch.ones(2, 3),
    "torch.eye(3)": torch.eye(3),
    "torch.arange(5)": torch.arange(5),
    "torch.linspace(0, 1, 5)": torch.linspace(0, 1, 5),
    "torch.randn(2, 3)": torch.randn(2, 3),
    "torch.rand(2, 3)": torch.rand(2, 3),
}

for name, tensor in creation_examples.items():
    print(f"\n{name}:")
    print(tensor)

print("\n" + "=" * 50)
print("PyTorch基础学习完成！")
print("接下来请运行 06_tensor_operations.py 学习张量运算")
print("=" * 50)
