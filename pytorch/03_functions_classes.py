#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python函数和类学习 - 第三部分
包含：函数定义、参数、返回值、类和对象、继承等
"""

print("=" * 50)
print("Python函数和类学习")
print("=" * 50)

# 1. 基本函数定义
print("\n1. 基本函数定义")
print("-" * 30)

def greet(name):
    """
    问候函数
    参数: name - 要问候的人的姓名
    返回: 问候语字符串clear
    """
    return f"你好, {name}!"

# 调用函数
message = greet("小明")
print(f"函数调用结果: {message}")

# 2. 函数参数类型
print("\n2. 函数参数类型")
print("-" * 30)

# 位置参数
def add(a, b):
    """两个数相加"""
    return a + b

result = add(3, 5)
print(f"位置参数: add(3, 5) = {result}")

# 关键字参数
def introduce(name, age, city="北京"):
    """自我介绍函数，city有默认值"""
    return f"我叫{name}，今年{age}岁，来自{city}"

intro1 = introduce("小红", 20)
intro2 = introduce(name="小李", age=25, city="上海")
print(f"默认参数: {intro1}")
print(f"关键字参数: {intro2}")

# 可变参数 *args
def sum_all(*numbers):
    """计算所有数字的和"""
    total = 0
    for num in numbers:
        total += num
    return total

result1 = sum_all(1, 2, 3)
result2 = sum_all(1, 2, 3, 4, 5)
print(f"可变参数: sum_all(1, 2, 3) = {result1}")
print(f"可变参数: sum_all(1, 2, 3, 4, 5) = {result2}")

# 关键字可变参数 **kwargs
def print_info(**info):
    """打印所有信息"""
    for key, value in info.items():
        print(f"  {key}: {value}")

print("关键字可变参数:")
print_info(姓名="张三", 年龄=30, 职业="程序员", 爱好="编程")

# 3. 函数作为对象
print("\n3. 函数作为对象")
print("-" * 30)

def square(x):
    """计算平方"""
    return x ** 2

def cube(x):
    """计算立方"""
    return x ** 3

# 函数可以赋值给变量
my_func = square
print(f"函数赋值: my_func(4) = {my_func(4)}")

# 函数可以作为参数传递
def apply_function(func, value):
    """应用函数到值上"""
    return print(func , value)

print(apply_function(square, 5))
result1 = apply_function(square, 5)
result2 = apply_function(cube, 3)
print(f"函数作为参数: apply_function(square, 5) = {result1}")
print(f"函数作为参数: apply_function(cube, 3) = {result2}")

# 4. Lambda函数（匿名函数）
print("\n4. Lambda函数")
print("-" * 30)

# 基本lambda函数
double = lambda x: x * 2
print(f"Lambda函数: double(6) = {double(6)}")

# lambda在列表操作中的应用
numbers = [1, 2, 3, 4, 5]
squared = list(map(lambda x: x**2, numbers))
even_numbers = list(filter(lambda x: x % 2 == 0, numbers))

print(f"原列表: {numbers}")
print(f"平方后: {squared}")
print(f"偶数: {even_numbers}")

# 5. 类的定义和使用
print("\n5. 类的定义和使用")
print("-" * 30)

class Student:
    """学生类"""
    
    # 类变量（所有实例共享）
    school = "Python学院"
    
    def __init__(self, name, age, student_id):
        """
        构造函数
        参数: name - 姓名, age - 年龄, student_id - 学号
        """
        # 实例变量（每个实例独有）
        self.name = name
        self.age = age
        self.student_id = student_id
        self.grades = []  # 成绩列表
    
    def add_grade(self, subject, score):
        """添加成绩"""
        self.grades.append({"科目": subject, "分数": score})
        print(f"  {self.name}的{subject}成绩{score}分已添加")
    
    def get_average(self):
        """计算平均分"""
        if not self.grades:
            return 0
        total = sum(grade["分数"] for grade in self.grades)
        return total / len(self.grades)
    
    def introduce(self):
        """自我介绍"""
        avg = self.get_average()
        return f"我是{self.name}，{self.age}岁，学号{self.student_id}，平均分{avg:.1f}"
    
    def __str__(self):
        """字符串表示"""
        return f"Student({self.name}, {self.age}岁)"

# 创建学生对象
student1 = Student("小明", 18, "2023001")
student2 = Student("小红", 19, "2023002")

print(f"创建学生: {student1}")
print(f"创建学生: {student2}")
print(f"学校: {Student.school}")

# 添加成绩
student1.add_grade("数学", 85)
student1.add_grade("英语", 92)
student2.add_grade("数学", 78)
student2.add_grade("英语", 88)

# 获取信息
print(f"学生1信息: {student1.introduce()}")
print(f"学生2信息: {student2.introduce()}")

# 6. 继承
print("\n6. 继承")
print("-" * 30)

class Person:
    """人类基类"""
    
    def __init__(self, name, age):
        self.name = name
        self.age = age
    
    def speak(self):
        return f"{self.name}说话了"
    
    def walk(self):
        return f"{self.name}在走路"

class Teacher(Person):
    """教师类，继承自Person"""
    
    def __init__(self, name, age, subject):
        super().__init__(name, age)  # 调用父类构造函数
        self.subject = subject
        self.students = []
    
    def teach(self):
        """教学方法"""
        return f"{self.name}老师正在教{self.subject}"
    
    def add_student(self, student):
        """添加学生"""
        self.students.append(student)
        print(f"  {student.name}加入了{self.name}老师的班级")
    
    def speak(self):
        """重写父类方法"""
        return f"{self.name}老师在讲课"

# 创建教师对象
teacher = Teacher("王老师", 35, "Python编程")
print(f"创建教师: {teacher.name}, {teacher.age}岁, 教{teacher.subject}")

# 使用继承的方法
print(f"继承的方法: {teacher.walk()}")
print(f"重写的方法: {teacher.speak()}")
print(f"新增的方法: {teacher.teach()}")

# 添加学生到教师班级
teacher.add_student(student1)
teacher.add_student(student2)

# 7. 特殊方法（魔法方法）
print("\n7. 特殊方法")
print("-" * 30)

class Book:
    """书籍类，演示特殊方法"""
    
    def __init__(self, title, author, pages):
        self.title = title
        self.author = author
        self.pages = pages
    
    def __str__(self):
        """字符串表示"""
        return f"《{self.title}》- {self.author}"
    
    def __len__(self):
        """长度（页数）"""
        return self.pages
    
    def __eq__(self, other):
        """相等比较"""
        if isinstance(other, Book):
            return self.title == other.title and self.author == other.author
        return False
    
    def __lt__(self, other):
        """小于比较（按页数）"""
        if isinstance(other, Book):
            return self.pages < other.pages
        return False

# 创建书籍对象
book1 = Book("Python入门", "张三", 300)
book2 = Book("Python进阶", "李四", 450)
book3 = Book("Python入门", "张三", 300)

print(f"书籍1: {book1}")
print(f"书籍2: {book2}")
print(f"书籍1页数: {len(book1)}")
print(f"书籍1和书籍3相等: {book1 == book3}")
print(f"书籍1比书籍2页数少: {book1 < book2}")

# 8. 实践练习
print("\n8. 实践练习 - 简单的银行账户系统")
print("-" * 30)

class BankAccount:
    """银行账户类"""
    
    def __init__(self, account_number, owner, initial_balance=0):
        self.account_number = account_number
        self.owner = owner
        self.balance = initial_balance
        self.transaction_history = []
    
    def deposit(self, amount):
        """存款"""
        if amount > 0:
            self.balance += amount
            self.transaction_history.append(f"存款: +{amount}")
            return f"存款成功，当前余额: {self.balance}"
        return "存款金额必须大于0"
    
    def withdraw(self, amount):
        """取款"""
        if amount > 0 and amount <= self.balance:
            self.balance -= amount
            self.transaction_history.append(f"取款: -{amount}")
            return f"取款成功，当前余额: {self.balance}"
        return "余额不足或取款金额无效"
    
    def get_balance(self):
        """查询余额"""
        return self.balance
    
    def get_history(self):
        """查询交易历史"""
        return self.transaction_history

# 创建银行账户
account = BankAccount("*********", "小明", 1000)
print(f"创建账户: {account.owner}的账户{account.account_number}")
print(f"初始余额: {account.get_balance()}")

# 进行交易
print(account.deposit(500))
print(account.withdraw(200))
print(account.withdraw(2000))  # 余额不足

print(f"最终余额: {account.get_balance()}")
print("交易历史:")
for transaction in account.get_history():
    print(f"  {transaction}")

print("\n" + "=" * 50)
print("Python函数和类学习完成！")
print("接下来请运行 04_data_structures.py")
print("=" * 50)
