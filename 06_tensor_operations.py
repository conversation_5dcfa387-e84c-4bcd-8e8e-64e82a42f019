#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTorch张量运算学习 - 第六部分
包含：数学运算、线性代数、自动微分等
"""

import torch
import numpy as np

print("=" * 50)
print("PyTorch张量运算学习")
print("=" * 50)

# 1. 基本数学运算
print("\n1. 基本数学运算")
print("-" * 30)

# 创建示例张量
a = torch.tensor([1.0, 2.0, 3.0])
b = torch.tensor([4.0, 5.0, 6.0])

print(f"张量a: {a}")
print(f"张量b: {b}")

# 基本运算
print(f"\n基本运算:")
print(f"加法: a + b = {a + b}")
print(f"减法: a - b = {a - b}")
print(f"乘法: a * b = {a * b}")
print(f"除法: a / b = {a / b}")
print(f"幂运算: a ** 2 = {a ** 2}")

# 就地运算（in-place operations）
c = a.clone()  # 复制张量
print(f"\n就地运算前: c = {c}")
c.add_(b)  # 就地加法，等价于 c += b
print(f"就地加法后: c = {c}")

# 2. 数学函数
print("\n2. 数学函数")
print("-" * 30)

x = torch.tensor([0.0, 1.0, 2.0, 3.0])
print(f"输入: x = {x}")

# 常用数学函数
print(f"\n数学函数:")
print(f"平方根: sqrt(x) = {torch.sqrt(x)}")
print(f"指数: exp(x) = {torch.exp(x)}")
print(f"对数: log(x+1) = {torch.log(x + 1)}")  # 避免log(0)
print(f"正弦: sin(x) = {torch.sin(x)}")
print(f"余弦: cos(x) = {torch.cos(x)}")
print(f"绝对值: abs(x-1.5) = {torch.abs(x - 1.5)}")

# 激活函数（在神经网络中常用）
print(f"\n激活函数:")
print(f"ReLU: max(0, x-1) = {torch.relu(x - 1)}")
print(f"Sigmoid: sigmoid(x) = {torch.sigmoid(x)}")
print(f"Tanh: tanh(x) = {torch.tanh(x)}")

# 3. 聚合运算
print("\n3. 聚合运算")
print("-" * 30)

# 创建2D张量
matrix = torch.randn(3, 4)
print(f"矩阵:\n{matrix}")

# 聚合函数
print(f"\n聚合运算:")
print(f"总和: {matrix.sum()}")
print(f"平均值: {matrix.mean()}")
print(f"最大值: {matrix.max()}")
print(f"最小值: {matrix.min()}")
print(f"标准差: {matrix.std()}")

# 按维度聚合
print(f"\n按维度聚合:")
print(f"按行求和 (dim=1): {matrix.sum(dim=1)}")
print(f"按列求和 (dim=0): {matrix.sum(dim=0)}")
print(f"按行求平均 (dim=1): {matrix.mean(dim=1)}")

# 4. 线性代数运算
print("\n4. 线性代数运算")
print("-" * 30)

# 矩阵乘法
A = torch.randn(3, 4)
B = torch.randn(4, 2)
print(f"矩阵A形状: {A.shape}")
print(f"矩阵B形状: {B.shape}")

# 矩阵乘法
C = torch.matmul(A, B)  # 或者使用 A @ B
print(f"矩阵乘法 A @ B 形状: {C.shape}")

# 向量运算
v1 = torch.tensor([1.0, 2.0, 3.0])
v2 = torch.tensor([4.0, 5.0, 6.0])

dot_product = torch.dot(v1, v2)  # 点积
print(f"\n向量运算:")
print(f"向量v1: {v1}")
print(f"向量v2: {v2}")
print(f"点积: {dot_product}")

# 矩阵的特殊运算
square_matrix = torch.randn(3, 3)
print(f"\n方阵运算:")
print(f"方阵:\n{square_matrix}")
print(f"行列式: {torch.det(square_matrix)}")
print(f"迹 (对角线元素和): {torch.trace(square_matrix)}")

# 5. 广播机制
print("\n5. 广播机制")
print("-" * 30)

# 广播允许不同形状的张量进行运算
a = torch.randn(3, 1)
b = torch.randn(1, 4)
print(f"张量a形状: {a.shape}")
print(f"张量b形状: {b.shape}")

c = a + b  # 广播：(3,1) + (1,4) -> (3,4)
print(f"广播结果形状: {c.shape}")

# 更多广播示例
scalar = torch.tensor(5.0)
vector = torch.tensor([1.0, 2.0, 3.0])
matrix = torch.randn(2, 3)

print(f"\n广播示例:")
print(f"标量 + 向量: {scalar + vector}")
print(f"向量 + 矩阵形状: {(vector + matrix).shape}")

# 6. 张量比较和逻辑运算
print("\n6. 张量比较和逻辑运算")
print("-" * 30)

x = torch.tensor([1, 2, 3, 4, 5])
y = torch.tensor([1, 3, 2, 4, 6])

print(f"x: {x}")
print(f"y: {y}")

# 比较运算
print(f"\n比较运算:")
print(f"x == y: {x == y}")
print(f"x > y: {x > y}")
print(f"x >= 3: {x >= 3}")

# 逻辑运算
mask1 = x > 2
mask2 = x < 5
print(f"\n逻辑运算:")
print(f"x > 2: {mask1}")
print(f"x < 5: {mask2}")
print(f"(x > 2) & (x < 5): {mask1 & mask2}")
print(f"(x > 2) | (x < 5): {mask1 | mask2}")

# 条件选择
result = torch.where(x > 3, x, torch.zeros_like(x))
print(f"条件选择 (x>3则保留，否则为0): {result}")

# 7. 自动微分 (Autograd)
print("\n7. 自动微分 (Autograd)")
print("-" * 30)

# 创建需要梯度的张量
x = torch.tensor([2.0], requires_grad=True)
print(f"输入x: {x}")
print(f"x.requires_grad: {x.requires_grad}")

# 定义函数 y = x^2 + 3x + 1
y = x**2 + 3*x + 1
print(f"y = x^2 + 3x + 1 = {y}")

# 计算梯度 dy/dx = 2x + 3
y.backward()
print(f"梯度 dy/dx = {x.grad}")
print(f"理论值 (2*2 + 3) = {2*2 + 3}")

# 多变量函数的梯度
print(f"\n多变量梯度:")
x = torch.tensor([1.0, 2.0], requires_grad=True)
y = torch.tensor([3.0, 4.0], requires_grad=True)

# z = x^2 + y^2 的和
z = (x**2 + y**2).sum()
print(f"z = sum(x^2 + y^2) = {z}")

z.backward()
print(f"dz/dx = {x.grad}")  # 应该是 [2*1, 2*2] = [2, 4]
print(f"dz/dy = {y.grad}")  # 应该是 [2*3, 2*4] = [6, 8]

# 8. 梯度计算的实际应用
print("\n8. 梯度计算的实际应用")
print("-" * 30)

def simple_optimization():
    """简单的优化示例：找到函数 f(x) = (x-3)^2 的最小值"""
    
    # 初始化参数
    x = torch.tensor([0.0], requires_grad=True)
    learning_rate = 0.1
    
    print("优化过程：找到 f(x) = (x-3)^2 的最小值")
    print("理论最小值在 x = 3")
    
    for i in range(10):
        # 前向传播：计算损失
        loss = (x - 3)**2
        
        # 清零梯度
        if x.grad is not None:
            x.grad.zero_()
        
        # 反向传播：计算梯度
        loss.backward()
        
        # 更新参数
        with torch.no_grad():  # 更新参数时不需要计算梯度
            x -= learning_rate * x.grad
        
        if i % 2 == 0:
            print(f"  步骤 {i}: x = {x.item():.4f}, loss = {loss.item():.4f}")
    
    print(f"  最终结果: x = {x.item():.4f}")

simple_optimization()

# 9. 张量的高级操作
print("\n9. 张量的高级操作")
print("-" * 30)

# 堆叠和连接
a = torch.randn(2, 3)
b = torch.randn(2, 3)

print(f"张量a:\n{a}")
print(f"张量b:\n{b}")

# 连接 (concatenate)
cat_dim0 = torch.cat([a, b], dim=0)  # 沿第0维连接
cat_dim1 = torch.cat([a, b], dim=1)  # 沿第1维连接

print(f"\n沿dim=0连接 (形状 {cat_dim0.shape}):\n{cat_dim0}")
print(f"\n沿dim=1连接 (形状 {cat_dim1.shape}):\n{cat_dim1}")

# 堆叠 (stack)
stack_dim0 = torch.stack([a, b], dim=0)  # 创建新维度
print(f"\n堆叠 (形状 {stack_dim0.shape}):\n{stack_dim0}")

# 分割
chunks = torch.chunk(cat_dim0, 2, dim=0)  # 分割为2块
print(f"\n分割结果:")
for i, chunk in enumerate(chunks):
    print(f"  块{i} 形状: {chunk.shape}")

# 10. 实践练习
print("\n10. 实践练习")
print("-" * 30)

def tensor_operations_practice():
    """张量运算综合练习"""
    
    print("练习1: 实现softmax函数")
    def softmax(x):
        """手动实现softmax函数"""
        exp_x = torch.exp(x - torch.max(x))  # 数值稳定性
        return exp_x / torch.sum(exp_x)
    
    logits = torch.tensor([1.0, 2.0, 3.0])
    manual_softmax = softmax(logits)
    pytorch_softmax = torch.softmax(logits, dim=0)
    
    print(f"  输入: {logits}")
    print(f"  手动实现: {manual_softmax}")
    print(f"  PyTorch实现: {pytorch_softmax}")
    print(f"  差异: {torch.abs(manual_softmax - pytorch_softmax).max()}")
    
    print("\n练习2: 计算两个向量的余弦相似度")
    def cosine_similarity(a, b):
        """计算余弦相似度"""
        dot_product = torch.dot(a, b)
        norm_a = torch.norm(a)
        norm_b = torch.norm(b)
        return dot_product / (norm_a * norm_b)
    
    vec1 = torch.tensor([1.0, 2.0, 3.0])
    vec2 = torch.tensor([4.0, 5.0, 6.0])
    similarity = cosine_similarity(vec1, vec2)
    
    print(f"  向量1: {vec1}")
    print(f"  向量2: {vec2}")
    print(f"  余弦相似度: {similarity:.4f}")
    
    print("\n练习3: 批量矩阵乘法")
    batch_size = 2
    A_batch = torch.randn(batch_size, 3, 4)
    B_batch = torch.randn(batch_size, 4, 2)
    
    # 批量矩阵乘法
    C_batch = torch.bmm(A_batch, B_batch)
    
    print(f"  批量A形状: {A_batch.shape}")
    print(f"  批量B形状: {B_batch.shape}")
    print(f"  批量乘法结果形状: {C_batch.shape}")

tensor_operations_practice()

print("\n" + "=" * 50)
print("PyTorch张量运算学习完成！")
print("接下来请运行 07_neural_networks.py 学习神经网络")
print("=" * 50)
