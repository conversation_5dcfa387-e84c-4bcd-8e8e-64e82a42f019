#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ray与PyTorch对比和结合 - 第二部分
包含：Ray vs PyTorch对比、Ray Train、数据并行、模型并行等
"""

import os
os.environ["TORCH_DISABLE_COMPILE"] = "1"

import ray
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import time
from typing import Dict, List
import os

print("=" * 50)
print("Ray与PyTorch对比和结合")
print("=" * 50)

# 确保Ray已初始化
if not ray.is_initialized():
    ray.init(ignore_reinit_error=True)

# 1. Ray vs PyTorch：核心差异
print("\n1. Ray vs PyTorch：核心差异")
print("-" * 30)

print("""
Ray和PyTorch的定位和差异：

PyTorch：
- 深度学习框架：专注于神经网络的构建、训练和推理
- 自动微分：提供autograd系统进行梯度计算
- GPU加速：原生支持CUDA进行GPU计算
- 模型定义：提供丰富的层和优化器
- 单机为主：原生分布式支持有限

Ray：
- 分布式计算框架：通用的并行和分布式计算
- 任务调度：智能的任务分配和资源管理
- 容错性：自动处理节点故障和任务重试
- 生态系统：包含训练、调优、推理等完整工具链
- 多机扩展：天然支持多机分布式计算

结合使用的优势：
- Ray处理分布式：负责任务分发、资源管理、容错
- PyTorch处理计算：负责模型定义、前向后向传播
- 最佳实践：Ray Train + PyTorch = 强大的分布式训练
""")

# 2. 简单的PyTorch模型
print("\n2. 创建PyTorch模型")
print("-" * 30)

class SimpleNet(nn.Module):
    """简单的神经网络"""
    
    def __init__(self, input_size=10, hidden_size=20, output_size=1):
        super(SimpleNet, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()
    
    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.relu(self.fc2(x))
        x = self.fc3(x)
        return x

# 创建模型和数据
model = SimpleNet()
print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")

# 生成示例数据
X = torch.randn(1000, 10)
y = torch.randn(1000, 1)

print(f"数据形状: X={X.shape}, y={y.shape}")

# 3. 传统PyTorch训练 vs Ray并行训练
print("\n3. 传统PyTorch训练 vs Ray并行训练")
print("-" * 30)

def train_pytorch_traditional(model, X, y, epochs=10):
    """传统PyTorch训练"""
    model.train()
    optimizer = optim.Adam(model.parameters(), lr=0.01)
    criterion = nn.MSELoss()
    
    start_time = time.time()
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X)
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()
        
        if epoch % 5 == 0:
            print(f"  Epoch {epoch}: Loss = {loss.item():.4f}")
    
    training_time = time.time() - start_time
    return training_time

@ray.remote
def train_pytorch_remote(model_state, X_chunk, y_chunk, epochs=10):
    """Ray远程训练函数"""
    # 在远程worker中重建模型
    model = SimpleNet()
    model.load_state_dict(model_state)
    model.train()
    
    optimizer = optim.Adam(model.parameters(), lr=0.01)
    criterion = nn.MSELoss()
    
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X_chunk)
        loss = criterion(outputs, y_chunk)
        loss.backward()
        optimizer.step()
    
    # 返回训练后的模型状态和最终损失
    return model.state_dict(), loss.item()

# 传统训练
print("传统PyTorch训练:")
traditional_time = train_pytorch_traditional(model, X, y)
print(f"传统训练时间: {traditional_time:.2f}秒")

# Ray并行训练（数据并行）
print("\nRay并行训练（数据并行）:")
start_time = time.time()

# 分割数据
num_workers = 4
chunk_size = len(X) // num_workers
model_state = model.state_dict()

futures = []
for i in range(num_workers):
    start_idx = i * chunk_size
    end_idx = start_idx + chunk_size if i < num_workers - 1 else len(X)
    
    X_chunk = X[start_idx:end_idx]
    y_chunk = y[start_idx:end_idx]
    
    future = train_pytorch_remote.remote(model_state, X_chunk, y_chunk)
    futures.append(future)

# 收集结果
results = ray.get(futures)
ray_time = time.time() - start_time

print(f"Ray并行训练时间: {ray_time:.2f}秒")
print(f"各worker最终损失: {[result[1] for result in results]}")

# 4. Ray Train简介
print("\n4. Ray Train简介")
print("-" * 30)

print("""
Ray Train是Ray生态系统中专门用于分布式机器学习训练的库：

主要特性：
1. 分布式训练：支持数据并行和模型并行
2. 容错性：自动处理worker故障
3. 弹性训练：动态调整worker数量
4. 多框架支持：PyTorch、TensorFlow、XGBoost等
5. 超参数调优：与Ray Tune集成

核心概念：
- Trainer：训练器，定义训练逻辑
- ScalingConfig：扩展配置，指定worker数量和资源
- RunConfig：运行配置，指定检查点、日志等
- TrainLoop：训练循环，在每个worker上执行

安装Ray Train：
pip install "ray[train]"
""")

# 检查Ray Train是否可用
try:
    from ray import train
    from ray.train import ScalingConfig, RunConfig
    from ray.train.torch import TorchTrainer
    print("Ray Train已安装并可用")
    ray_train_available = True
except ImportError:
    print("Ray Train未安装，跳过相关示例")
    print("请运行: pip install 'ray[train]' 来安装")
    ray_train_available = False

# 5. 使用Ray Train进行分布式训练
if ray_train_available:
    print("\n5. 使用Ray Train进行分布式训练")
    print("-" * 30)
    
    def train_func(config):
        """Ray Train训练函数"""
        import torch
        import torch.nn as nn
        import torch.optim as optim
        from ray import train
        
        # 获取分布式训练上下文
        world_size = train.get_context().get_world_size()
        rank = train.get_context().get_world_rank()
        
        print(f"Worker {rank}/{world_size} 开始训练")
        
        # 创建模型
        model = SimpleNet()
        
        # 包装为分布式模型
        model = train.torch.prepare_model(model)
        
        # 创建优化器
        optimizer = optim.Adam(model.parameters(), lr=0.01)
        
        # 生成本地数据（实际应用中应该是分布式数据加载）
        local_X = torch.randn(250, 10)
        local_y = torch.randn(250, 1)
        
        criterion = nn.MSELoss()
        
        # 训练循环
        for epoch in range(10):
            model.train()
            optimizer.zero_grad()
            
            outputs = model(local_X)
            loss = criterion(outputs, local_y)
            
            loss.backward()
            optimizer.step()
            
            # 报告指标（会自动聚合所有worker的结果）
            train.report({"loss": loss.item(), "epoch": epoch})
    
    # 配置分布式训练
    scaling_config = ScalingConfig(
        num_workers=2,  # worker数量
        use_gpu=False,  # 是否使用GPU
    )
    
    run_config = RunConfig(
        name="ray_train_example",
        storage_path="./ray_results"
    )
    
    # 创建训练器
    trainer = TorchTrainer(
        train_loop_per_worker=train_func,
        scaling_config=scaling_config,
        run_config=run_config,
    )
    
    print("开始Ray Train分布式训练...")
    try:
        result = trainer.fit()
        print(f"训练完成！最终结果: {result.metrics}")
    except Exception as e:
        print(f"训练过程中出现错误: {e}")

# 6. Ray vs PyTorch分布式对比
print("\n6. Ray vs PyTorch分布式对比")
print("-" * 30)

comparison_table = """
特性对比：

| 特性 | PyTorch DDP | Ray Train |
|------|-------------|-----------|
| 设置复杂度 | 中等 | 简单 |
| 容错性 | 有限 | 强大 |
| 弹性扩展 | 不支持 | 支持 |
| 多机部署 | 复杂 | 简单 |
| 资源管理 | 手动 | 自动 |
| 监控调试 | 基础 | 丰富 |
| 学习曲线 | 陡峭 | 平缓 |

使用建议：
- 单机多GPU：PyTorch DDP足够
- 多机训练：Ray Train更简单
- 需要容错：Ray Train更可靠
- 复杂调度：Ray Train更灵活
- 快速原型：Ray Train更快速
"""

print(comparison_table)

# 7. Ray中的模型推理
print("\n7. Ray中的模型推理")
print("-" * 30)

@ray.remote
class ModelServer:
    """Ray模型推理服务器"""
    
    def __init__(self, model_state_dict):
        # 在远程actor中加载模型
        self.model = SimpleNet()
        self.model.load_state_dict(model_state_dict)
        self.model.eval()
        print("模型服务器初始化完成")
    
    def predict(self, x):
        """模型推理"""
        with torch.no_grad():
            if isinstance(x, np.ndarray):
                x = torch.FloatTensor(x)
            return self.model(x).numpy()
    
    def batch_predict(self, batch_x):
        """批量推理"""
        with torch.no_grad():
            if isinstance(batch_x, np.ndarray):
                batch_x = torch.FloatTensor(batch_x)
            return self.model(batch_x).numpy()

# 创建模型服务器
print("创建Ray模型推理服务器...")
model_server = ModelServer.remote(model.state_dict())

# 单个推理
test_input = np.random.randn(1, 10)
prediction_future = model_server.predict.remote(test_input)
prediction = ray.get(prediction_future)
print(f"单个推理结果: {prediction.flatten()}")

# 批量推理
batch_input = np.random.randn(5, 10)
batch_prediction_future = model_server.batch_predict.remote(batch_input)
batch_predictions = ray.get(batch_prediction_future)
print(f"批量推理结果形状: {batch_predictions.shape}")

# 8. 并行推理性能对比
print("\n8. 并行推理性能对比")
print("-" * 30)

def sequential_inference(model, data_batches):
    """串行推理"""
    model.eval()
    results = []
    start_time = time.time()
    
    with torch.no_grad():
        for batch in data_batches:
            result = model(torch.FloatTensor(batch))
            results.append(result.numpy())
    
    elapsed_time = time.time() - start_time
    return results, elapsed_time

@ray.remote
def parallel_inference_worker(model_state, data_batch):
    """并行推理worker"""
    model = SimpleNet()
    model.load_state_dict(model_state)
    model.eval()
    
    with torch.no_grad():
        result = model(torch.FloatTensor(data_batch))
        return result.numpy()

# 生成测试数据
num_batches = 8
batch_size = 100
test_batches = [np.random.randn(batch_size, 10) for _ in range(num_batches)]

# 串行推理
print("串行推理测试...")
seq_results, seq_time = sequential_inference(model, test_batches)
print(f"串行推理时间: {seq_time:.3f}秒")

# 并行推理
print("并行推理测试...")
start_time = time.time()
futures = []
for batch in test_batches:
    future = parallel_inference_worker.remote(model.state_dict(), batch)
    futures.append(future)

par_results = ray.get(futures)
par_time = time.time() - start_time
print(f"并行推理时间: {par_time:.3f}秒")
print(f"推理加速比: {seq_time / par_time:.2f}x")

# 9. 内存管理和优化
print("\n9. 内存管理和优化")
print("-" * 30)

# 使用ray.put()避免重复序列化大对象
large_model_state = model.state_dict()
print(f"模型状态大小: {sum(p.numel() for p in large_model_state.values())} 参数")

# 方法1：每次传递模型状态（低效）
print("方法1：重复传递模型状态")
start_time = time.time()
futures = []
for i in range(4):
    future = parallel_inference_worker.remote(large_model_state, test_batches[i])
    futures.append(future)
results1 = ray.get(futures)
time1 = time.time() - start_time

# 方法2：使用ray.put()共享模型状态（高效）
print("方法2：使用ray.put()共享模型状态")
start_time = time.time()
model_state_ref = ray.put(large_model_state)  # 放入对象存储
futures = []
for i in range(4):
    future = parallel_inference_worker.remote(model_state_ref, test_batches[i])
    futures.append(future)
results2 = ray.get(futures)
time2 = time.time() - start_time

print(f"方法1时间: {time1:.3f}秒")
print(f"方法2时间: {time2:.3f}秒")
print(f"优化效果: {time1 / time2:.2f}x 加速")

# 10. 最佳实践总结
print("\n10. 最佳实践总结")
print("-" * 30)

best_practices = """
Ray + PyTorch 最佳实践：

1. 数据管理：
   - 使用ray.put()共享大对象（模型权重、数据集）
   - 避免在任务间传递大量数据
   - 使用Ray Datasets处理大规模数据

2. 模型管理：
   - 在远程actor中加载模型，避免重复加载
   - 使用模型状态字典而不是完整模型对象
   - 考虑模型量化和压缩

3. 资源配置：
   - 合理设置num_cpus和num_gpus
   - 考虑内存使用和带宽限制
   - 使用placement groups控制任务放置

4. 错误处理：
   - 实现重试机制
   - 监控任务状态和资源使用
   - 使用Ray的容错特性

5. 性能优化：
   - 批量处理减少通信开销
   - 使用异步操作提高并发
   - 监控和调优资源使用

下一步学习：
- 运行 03_ray_distributed_training.py 学习完整的分布式训练
"""

print(best_practices)

print("\n" + "=" * 50)
print("Ray与PyTorch对比和结合学习完成！")
print("接下来请运行 03_ray_distributed_training.py")
print("=" * 50)
