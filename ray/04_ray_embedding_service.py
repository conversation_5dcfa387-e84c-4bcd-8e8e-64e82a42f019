#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ray Embedding推理服务 - 第四部分
包含：embedding模型、<PERSON>、批量推理、性能优化等
"""

import ray
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import time
import asyncio
from typing import List, Dict, Any, Optional
import json
import requests
from dataclasses import dataclass

print("=" * 50)
print("Ray Embedding推理服务")
print("=" * 50)

# 确保Ray已初始化
if not ray.is_initialized():
    ray.init(ignore_reinit_error=True)

# 1. Embedding模型定义
print("\n1. Embedding模型定义")
print("-" * 30)

class TextEmbeddingModel(nn.Module):
    """文本Embedding模型"""
    
    def __init__(self, vocab_size=10000, embedding_dim=256, hidden_dim=512, output_dim=128):
        super(TextEmbeddingModel, self).__init__()
        
        # 词嵌入层
        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx=0)
        
        # 编码器层
        self.encoder = nn.Sequential(
            nn.Linear(embedding_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, output_dim)
        )
        
        # 输出归一化
        self.layer_norm = nn.LayerNorm(output_dim)
        
    def forward(self, input_ids, attention_mask=None):
        """
        前向传播
        Args:
            input_ids: [batch_size, seq_len] 输入token ids
            attention_mask: [batch_size, seq_len] 注意力掩码
        Returns:
            embeddings: [batch_size, output_dim] 文本嵌入向量
        """
        # 词嵌入
        embeddings = self.embedding(input_ids)  # [batch_size, seq_len, embedding_dim]
        
        # 平均池化（简单的文本表示方法）
        if attention_mask is not None:
            # 使用注意力掩码进行加权平均
            mask_expanded = attention_mask.unsqueeze(-1).expand(embeddings.size()).float()
            sum_embeddings = torch.sum(embeddings * mask_expanded, dim=1)
            sum_mask = torch.clamp(mask_expanded.sum(dim=1), min=1e-9)
            mean_embeddings = sum_embeddings / sum_mask
        else:
            # 简单平均
            mean_embeddings = torch.mean(embeddings, dim=1)
        
        # 编码
        output = self.encoder(mean_embeddings)
        
        # 归一化
        output = self.layer_norm(output)
        
        # L2归一化（用于相似度计算）
        output = F.normalize(output, p=2, dim=1)
        
        return output

# 创建并初始化模型
print("创建Embedding模型...")
embedding_model = TextEmbeddingModel(
    vocab_size=10000,
    embedding_dim=256,
    hidden_dim=512,
    output_dim=128
)

# 模拟预训练权重（实际应用中应该加载真实的预训练模型）
print("初始化模型权重...")
with torch.no_grad():
    for param in embedding_model.parameters():
        if param.dim() > 1:
            nn.init.xavier_uniform_(param)
        else:
            nn.init.zeros_(param)

print(f"模型参数数量: {sum(p.numel() for p in embedding_model.parameters()):,}")

# 2. 数据预处理工具
print("\n2. 数据预处理工具")
print("-" * 30)

class TextTokenizer:
    """简单的文本分词器"""
    
    def __init__(self, vocab_size=10000, max_length=128):
        self.vocab_size = vocab_size
        self.max_length = max_length
        
        # 特殊token
        self.pad_token_id = 0
        self.unk_token_id = 1
        self.cls_token_id = 2
        self.sep_token_id = 3
        
        # 模拟词汇表（实际应用中应该使用真实的词汇表）
        self.vocab = {f"word_{i}": i + 4 for i in range(vocab_size - 4)}
        self.vocab.update({
            "[PAD]": self.pad_token_id,
            "[UNK]": self.unk_token_id,
            "[CLS]": self.cls_token_id,
            "[SEP]": self.sep_token_id
        })
        
        self.id_to_token = {v: k for k, v in self.vocab.items()}
    
    def tokenize(self, text: str) -> List[str]:
        """简单分词"""
        # 简单的空格分词（实际应用中应该使用更复杂的分词器）
        tokens = text.lower().split()
        return tokens
    
    def encode(self, text: str, add_special_tokens=True) -> Dict[str, torch.Tensor]:
        """编码文本为token ids"""
        tokens = self.tokenize(text)
        
        if add_special_tokens:
            tokens = ["[CLS]"] + tokens + ["[SEP]"]
        
        # 转换为ids
        input_ids = []
        for token in tokens:
            if token in self.vocab:
                input_ids.append(self.vocab[token])
            else:
                input_ids.append(self.unk_token_id)
        
        # 截断或填充
        if len(input_ids) > self.max_length:
            input_ids = input_ids[:self.max_length]
        
        attention_mask = [1] * len(input_ids)
        
        # 填充
        while len(input_ids) < self.max_length:
            input_ids.append(self.pad_token_id)
            attention_mask.append(0)
        
        return {
            "input_ids": torch.tensor(input_ids, dtype=torch.long),
            "attention_mask": torch.tensor(attention_mask, dtype=torch.long)
        }
    
    def batch_encode(self, texts: List[str]) -> Dict[str, torch.Tensor]:
        """批量编码"""
        batch_input_ids = []
        batch_attention_mask = []
        
        for text in texts:
            encoded = self.encode(text)
            batch_input_ids.append(encoded["input_ids"])
            batch_attention_mask.append(encoded["attention_mask"])
        
        return {
            "input_ids": torch.stack(batch_input_ids),
            "attention_mask": torch.stack(batch_attention_mask)
        }

# 创建分词器
tokenizer = TextTokenizer()

# 测试分词器
test_texts = [
    "hello world this is a test",
    "ray is awesome for distributed computing",
    "pytorch and ray work great together"
]

print("测试分词器:")
for text in test_texts:
    encoded = tokenizer.encode(text)
    print(f"文本: '{text}'")
    print(f"Token IDs: {encoded['input_ids'][:10]}...")  # 只显示前10个
    print(f"Attention Mask: {encoded['attention_mask'][:10]}...")
    print()

# 3. Ray Serve推理服务
print("\n3. Ray Serve推理服务")
print("-" * 30)

# 检查Ray Serve是否可用
try:
    from ray import serve
    print("Ray Serve已安装并可用")
    serve_available = True
except ImportError:
    print("Ray Serve未安装，使用Ray Actor模拟")
    print("请运行: pip install 'ray[serve]' 来安装Ray Serve")
    serve_available = False

if serve_available:
    # 启动Ray Serve
    serve.start(detached=True, http_options={"host": "0.0.0.0", "port": 8000})
    
    @serve.deployment(num_replicas=2, ray_actor_options={"num_cpus": 1})
    class EmbeddingService:
        """Ray Serve Embedding服务"""
        
        def __init__(self):
            # 加载模型和分词器
            self.model = TextEmbeddingModel()
            self.model.eval()
            self.tokenizer = TextTokenizer()
            
            # 模拟加载预训练权重
            with torch.no_grad():
                for param in self.model.parameters():
                    if param.dim() > 1:
                        nn.init.xavier_uniform_(param)
                    else:
                        nn.init.zeros_(param)
            
            print("EmbeddingService初始化完成")
        
        async def __call__(self, request):
            """处理HTTP请求"""
            # 解析请求
            if hasattr(request, 'json'):
                data = await request.json()
            else:
                data = request
            
            texts = data.get("texts", [])
            if isinstance(texts, str):
                texts = [texts]
            
            # 生成嵌入
            embeddings = self.get_embeddings(texts)
            
            return {
                "embeddings": embeddings.tolist(),
                "shape": list(embeddings.shape)
            }
        
        def get_embeddings(self, texts: List[str]) -> np.ndarray:
            """获取文本嵌入"""
            if not texts:
                return np.array([])
            
            # 批量编码
            encoded = self.tokenizer.batch_encode(texts)
            
            # 模型推理
            with torch.no_grad():
                embeddings = self.model(
                    encoded["input_ids"],
                    encoded["attention_mask"]
                )
            
            return embeddings.numpy()
    
    # 部署服务
    embedding_service = EmbeddingService.bind()
    serve.run(embedding_service, name="embedding_service", route_prefix="/embed")
    
    print("Ray Serve Embedding服务已启动")
    print("服务地址: http://localhost:8000/embed")

else:
    # 使用Ray Actor模拟服务
    @ray.remote(num_cpus=1)
    class EmbeddingServiceActor:
        """Ray Actor Embedding服务"""
        
        def __init__(self):
            self.model = TextEmbeddingModel()
            self.model.eval()
            self.tokenizer = TextTokenizer()
            
            # 模拟加载预训练权重
            with torch.no_grad():
                for param in self.model.parameters():
                    if param.dim() > 1:
                        nn.init.xavier_uniform_(param)
                    else:
                        nn.init.zeros_(param)
            
            print("EmbeddingServiceActor初始化完成")
        
        def get_embeddings(self, texts: List[str]) -> np.ndarray:
            """获取文本嵌入"""
            if not texts:
                return np.array([])
            
            # 批量编码
            encoded = self.tokenizer.batch_encode(texts)
            
            # 模型推理
            with torch.no_grad():
                embeddings = self.model(
                    encoded["input_ids"],
                    encoded["attention_mask"]
                )
            
            return embeddings.numpy()
    
    # 创建服务实例
    embedding_service_actor = EmbeddingServiceActor.remote()
    print("Ray Actor Embedding服务已创建")

# 4. 批量推理优化
print("\n4. 批量推理优化")
print("-" * 30)

@ray.remote(num_cpus=1)
class BatchedEmbeddingService:
    """批量优化的Embedding服务"""
    
    def __init__(self, max_batch_size=32, max_wait_time=0.1):
        self.model = TextEmbeddingModel()
        self.model.eval()
        self.tokenizer = TextTokenizer()
        self.max_batch_size = max_batch_size
        self.max_wait_time = max_wait_time
        
        # 批处理队列
        self.pending_requests = []
        self.request_futures = []
        
        # 模拟加载预训练权重
        with torch.no_grad():
            for param in self.model.parameters():
                if param.dim() > 1:
                    nn.init.xavier_uniform_(param)
                else:
                    nn.init.zeros_(param)
        
        print(f"BatchedEmbeddingService初始化完成 (max_batch_size={max_batch_size})")
    
    def process_batch(self, texts_batch: List[str]) -> np.ndarray:
        """处理一个批次"""
        if not texts_batch:
            return np.array([])
        
        # 批量编码
        encoded = self.tokenizer.batch_encode(texts_batch)
        
        # 模型推理
        with torch.no_grad():
            embeddings = self.model(
                encoded["input_ids"],
                encoded["attention_mask"]
            )
        
        return embeddings.numpy()
    
    def get_embedding(self, text: str) -> np.ndarray:
        """获取单个文本的嵌入"""
        return self.process_batch([text])[0]
    
    def get_embeddings_batch(self, texts: List[str]) -> np.ndarray:
        """获取批量文本的嵌入"""
        return self.process_batch(texts)

# 创建批量服务
batched_service = BatchedEmbeddingService.remote(max_batch_size=16)

# 5. 性能测试和对比
print("\n5. 性能测试和对比")
print("-" * 30)

def generate_test_texts(num_texts=100):
    """生成测试文本"""
    templates = [
        "this is a sample text about {}",
        "machine learning with {} is powerful",
        "distributed computing using {} framework",
        "natural language processing for {}",
        "deep learning models for {} applications"
    ]
    
    topics = ["AI", "data science", "technology", "research", "innovation", 
              "algorithms", "neural networks", "optimization", "automation"]
    
    texts = []
    for i in range(num_texts):
        template = templates[i % len(templates)]
        topic = topics[i % len(topics)]
        text = template.format(topic) + f" example {i}"
        texts.append(text)
    
    return texts

# 生成测试数据
test_texts = generate_test_texts(100)
print(f"生成了{len(test_texts)}个测试文本")

# 单机推理性能测试
def test_local_inference(texts, batch_size=16):
    """测试本地推理性能"""
    model = TextEmbeddingModel()
    model.eval()
    tokenizer_local = TextTokenizer()
    
    start_time = time.time()
    all_embeddings = []
    
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i + batch_size]
        encoded = tokenizer_local.batch_encode(batch_texts)
        
        with torch.no_grad():
            embeddings = model(encoded["input_ids"], encoded["attention_mask"])
            all_embeddings.append(embeddings.numpy())
    
    total_time = time.time() - start_time
    final_embeddings = np.vstack(all_embeddings)
    
    return final_embeddings, total_time

# Ray分布式推理性能测试
def test_ray_inference(texts, service_actor):
    """测试Ray推理性能"""
    start_time = time.time()
    
    # 分批提交任务
    batch_size = 16
    futures = []
    
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i + batch_size]
        future = service_actor.get_embeddings_batch.remote(batch_texts)
        futures.append(future)
    
    # 收集结果
    results = ray.get(futures)
    total_time = time.time() - start_time
    
    final_embeddings = np.vstack(results)
    return final_embeddings, total_time

print("开始性能测试...")

# 本地推理测试
print("测试本地推理...")
local_embeddings, local_time = test_local_inference(test_texts[:50])  # 减少测试数量以节省时间

# Ray推理测试
print("测试Ray推理...")
ray_embeddings, ray_time = test_ray_inference(test_texts[:50], batched_service)

print(f"\n性能对比结果:")
print(f"本地推理时间: {local_time:.3f}秒")
print(f"Ray推理时间: {ray_time:.3f}秒")
print(f"加速比: {local_time / ray_time:.2f}x")
print(f"嵌入维度: {local_embeddings.shape}")

# 验证结果一致性
embedding_diff = np.abs(local_embeddings - ray_embeddings).mean()
print(f"嵌入差异 (平均绝对误差): {embedding_diff:.6f}")

# 6. 相似度搜索服务
print("\n6. 相似度搜索服务")
print("-" * 30)

@ray.remote
class SimilaritySearchService:
    """相似度搜索服务"""
    
    def __init__(self):
        self.embedding_service = BatchedEmbeddingService.remote()
        self.document_embeddings = None
        self.documents = []
        
        print("SimilaritySearchService初始化完成")
    
    def index_documents(self, documents: List[str]):
        """索引文档"""
        print(f"开始索引{len(documents)}个文档...")
        
        self.documents = documents
        
        # 获取所有文档的嵌入
        embeddings_future = self.embedding_service.get_embeddings_batch.remote(documents)
        self.document_embeddings = ray.get(embeddings_future)
        
        print(f"文档索引完成，嵌入形状: {self.document_embeddings.shape}")
    
    def search(self, query: str, top_k: int = 5):
        """搜索相似文档"""
        if self.document_embeddings is None:
            return []
        
        # 获取查询嵌入
        query_embedding_future = self.embedding_service.get_embedding.remote(query)
        query_embedding = ray.get(query_embedding_future)
        
        # 计算相似度
        similarities = np.dot(self.document_embeddings, query_embedding)
        
        # 获取top-k结果
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            results.append({
                "document": self.documents[idx],
                "similarity": float(similarities[idx]),
                "index": int(idx)
            })
        
        return results

# 创建搜索服务
search_service = SimilaritySearchService.remote()

# 准备文档库
documents = [
    "machine learning algorithms for data analysis",
    "deep learning neural networks and applications",
    "distributed computing with ray framework",
    "natural language processing techniques",
    "computer vision and image recognition",
    "reinforcement learning for game playing",
    "data science and statistical analysis",
    "artificial intelligence research trends",
    "pytorch framework for deep learning",
    "cloud computing and scalability"
]

# 索引文档
ray.get(search_service.index_documents.remote(documents))

# 测试搜索
test_queries = [
    "deep learning models",
    "distributed systems",
    "data analysis methods"
]

print("测试相似度搜索:")
for query in test_queries:
    print(f"\n查询: '{query}'")
    results = ray.get(search_service.search.remote(query, top_k=3))
    
    for i, result in enumerate(results, 1):
        print(f"  {i}. 相似度: {result['similarity']:.4f}")
        print(f"     文档: {result['document']}")

# 7. 服务监控和指标
print("\n7. 服务监控和指标")
print("-" * 30)

@ray.remote
class EmbeddingServiceWithMetrics:
    """带监控指标的Embedding服务"""
    
    def __init__(self):
        self.model = TextEmbeddingModel()
        self.model.eval()
        self.tokenizer = TextTokenizer()
        
        # 指标统计
        self.metrics = {
            "total_requests": 0,
            "total_texts_processed": 0,
            "total_inference_time": 0.0,
            "average_batch_size": 0.0,
            "average_inference_time": 0.0
        }
        
        print("EmbeddingServiceWithMetrics初始化完成")
    
    def get_embeddings(self, texts: List[str]) -> np.ndarray:
        """获取嵌入并记录指标"""
        start_time = time.time()
        
        # 更新请求统计
        self.metrics["total_requests"] += 1
        self.metrics["total_texts_processed"] += len(texts)
        
        # 批量编码
        encoded = self.tokenizer.batch_encode(texts)
        
        # 模型推理
        with torch.no_grad():
            embeddings = self.model(
                encoded["input_ids"],
                encoded["attention_mask"]
            )
        
        # 更新时间统计
        inference_time = time.time() - start_time
        self.metrics["total_inference_time"] += inference_time
        
        # 计算平均值
        self.metrics["average_batch_size"] = (
            self.metrics["total_texts_processed"] / self.metrics["total_requests"]
        )
        self.metrics["average_inference_time"] = (
            self.metrics["total_inference_time"] / self.metrics["total_requests"]
        )
        
        return embeddings.numpy()
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取服务指标"""
        return self.metrics.copy()
    
    def reset_metrics(self):
        """重置指标"""
        for key in self.metrics:
            self.metrics[key] = 0.0

# 创建带监控的服务
monitored_service = EmbeddingServiceWithMetrics.remote()

# 测试监控功能
print("测试服务监控...")
test_batches = [
    test_texts[:10],
    test_texts[10:25],
    test_texts[25:40]
]

for i, batch in enumerate(test_batches, 1):
    print(f"处理批次 {i} ({len(batch)} 个文本)...")
    ray.get(monitored_service.get_embeddings.remote(batch))

# 获取指标
metrics = ray.get(monitored_service.get_metrics.remote())
print(f"\n服务指标:")
for key, value in metrics.items():
    if isinstance(value, float):
        print(f"  {key}: {value:.4f}")
    else:
        print(f"  {key}: {value}")

# 8. 部署和扩展建议
print("\n8. 部署和扩展建议")
print("-" * 30)

deployment_guide = """
Ray Embedding服务部署指南：

1. 生产环境部署：
   - 使用Ray Serve进行HTTP服务部署
   - 配置多个副本实现负载均衡
   - 使用GPU加速推理（如果可用）
   - 设置健康检查和自动重启

2. 性能优化：
   - 批量处理减少推理开销
   - 使用模型量化减少内存使用
   - 实现嵌入缓存避免重复计算
   - 优化数据预处理流程

3. 扩展策略：
   - 水平扩展：增加更多worker节点
   - 垂直扩展：使用更强大的硬件
   - 模型并行：大模型分片部署
   - 异步处理：使用消息队列解耦

4. 监控和运维：
   - 监控推理延迟和吞吐量
   - 跟踪资源使用情况
   - 设置告警和日志记录
   - 定期备份和更新模型

5. 安全考虑：
   - API认证和授权
   - 输入验证和清理
   - 速率限制防止滥用
   - 数据隐私保护

示例部署命令：
ray start --head --port=6379
python -m ray.serve run embedding_service:app --host=0.0.0.0 --port=8000
"""

print(deployment_guide)

# 9. 清理资源
print("\n9. 服务总结")
print("-" * 30)

print(f"""
Ray Embedding服务总结：

服务组件：
- TextEmbeddingModel: 文本嵌入模型
- TextTokenizer: 文本预处理器
- EmbeddingService: 基础推理服务
- BatchedEmbeddingService: 批量优化服务
- SimilaritySearchService: 相似度搜索服务

性能特点：
- 支持批量推理优化
- 分布式并行处理
- 自动负载均衡
- 实时监控指标

应用场景：
- 文本相似度搜索
- 文档检索系统
- 推荐系统
- 语义匹配

下一步：
- 集成真实的预训练模型
- 添加更多文本预处理功能
- 实现更复杂的相似度算法
- 部署到生产环境
""")

# 关闭Ray Serve（如果启动了）
if serve_available:
    try:
        serve.shutdown()
        print("Ray Serve已关闭")
    except:
        pass

print("\n" + "=" * 50)
print("Ray Embedding推理服务学习完成！")
print("恭喜你完成了Ray框架的完整学习！")
print("=" * 50)
