# Ray学习项目依赖包

# Ray核心包
ray[default]>=2.8.0
ray[train]>=2.8.0
ray[serve]>=2.8.0
ray[tune]>=2.8.0

# PyTorch相关
torch>=2.0.0
torchvision>=0.15.0

# 数据处理
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0

# Web服务
fastapi>=0.68.0
uvicorn>=0.15.0
requests>=2.25.0

# 工具包
tqdm>=4.62.0
psutil>=5.8.0
aiohttp>=3.8.0

# 开发工具
jupyter>=1.0.0
ipython>=7.0.0

# 可选：GPU支持
# torch-audio  # 如果需要音频处理
# transformers>=4.0.0  # 如果需要预训练模型
