import ray
import torch
import torch.nn as nn
import torch.optim as optim
from torch.nn.parameter import Parameter

# 初始化Ray
ray.init()

# 定义PyTorch模型类（将被包装为Ray Actor）
@ray.remote(num_cpus=1)  # 每个Actor使用0.25个GPU
class ModelWorker:
    def __init__(self, lr=0.01):
        # 在Actor内部创建模型
        self.model = nn.Sequential(
            nn.Linear(1, 10),
            nn.ReLU(),
            nn.Linear(10, 1)
        )
        self.optimizer = optim.Adam(self.model.parameters(), lr=lr)
        self.criterion = nn.MSELoss()

    def train_step(self, data):
        """在分片数据上执行一次训练步骤"""
        X, y = data
        self.optimizer.zero_grad()
        outputs = self.model(X)
        loss = self.criterion(outputs, y)
        loss.backward()
        self.optimizer.step()
        return loss.item()

    def get_weights(self):
        """获取模型权重（用于聚合）"""
        return [param.data.cpu().numpy() for param in self.model.parameters()]

    def set_weights(self, weights):
        """设置模型权重（来自参数服务器）"""
        for param, new_weight in zip(self.model.parameters(), weights):
            param.data = torch.tensor(new_weight).to(param.data.device)

# 创建参数服务器Actor
@ray.remote
class ParameterServer:
    def __init__(self, num_workers):
        self.global_weights = None
        self.num_workers = num_workers
        self.updates_received = 0

    def update_weights(self, worker_weights):
        """聚合工作节点的权重更新（简单平均）"""
        if self.global_weights is None:
            self.global_weights = worker_weights
        else:
            # 计算权重平均值
            new_weights = []
            for i in range(len(self.global_weights)):
                layer_sum = self.global_weights[i] * self.updates_received
                layer_sum += worker_weights[i]
                new_weights.append(layer_sum / (self.updates_received + 1))
            self.global_weights = new_weights

        self.updates_received = (self.updates_received + 1) % self.num_workers
        return self.global_weights

    def get_weights(self):
        return self.global_weights

# 准备分布式训练
def prepare_data(num_shards=4):
    """将数据分为多个分片"""
    X = torch.tensor([[i] for i in range(1, 101)], dtype=torch.float32)
    y = torch.tensor([[i*2] for i in range(1, 101)], dtype=torch.float32)

    shard_size = len(X) // num_shards
    return [
        (X[i*shard_size:(i+1)*shard_size],
         y[i*shard_size:(i+1)*shard_size])
        for i in range(num_shards)
    ]

# 主训练流程
def distributed_training(num_workers=4, epochs=10):
    # 准备数据分片
    data_shards = prepare_data(num_workers)

    # 创建参数服务器
    ps = ParameterServer.remote(num_workers)

    # 创建工作节点
    workers = [ModelWorker.remote() for _ in range(num_workers)]

    # 初始化：将相同权重分发给所有工作节点
    initial_weights = ray.get(workers[0].get_weights.remote())
    ray.get(ps.update_weights.remote(initial_weights))

    # 分布式训练循环
    for epoch in range(epochs):
        # 1. 将最新全局权重分发给所有工作节点
        current_weights = ray.get(ps.get_weights.remote())
        ray.get([worker.set_weights.remote(current_weights) for worker in workers])

        # 2. 并行执行训练步骤（每个工作节点处理一个数据分片）
        losses = []
        for i, worker in enumerate(workers):
            loss = worker.train_step.remote(data_shards[i])
            losses.append(loss)

        # 3. 收集并打印损失
        epoch_losses = ray.get(losses)
        avg_loss = sum(epoch_losses) / len(epoch_losses)
        print(f"Epoch {epoch+1}/{epochs}, Avg Loss: {avg_loss:.4f}")

        # 4. 聚合权重更新
        new_weights = ray.get([worker.get_weights.remote() for worker in workers])
        for weights in new_weights:
            ray.get(ps.update_weights.remote(weights))

    print("Distributed training completed!")

# 执行分布式训练
distributed_training(num_workers=4, epochs=10)

# 关闭Ray
ray.shutdown()