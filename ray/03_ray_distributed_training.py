#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ray分布式训练实战 - 第三部分
包含：完整的分布式训练流程、数据并行、模型检查点、监控等
"""

import ray
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, DistributedSampler
import numpy as np
import time
import os
import json
from typing import Dict, Any
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

print("=" * 50)
print("Ray分布式训练实战")
print("=" * 50)

# 确保Ray已初始化
if not ray.is_initialized():
    ray.init(ignore_reinit_error=True)

# 1. 准备分布式训练数据
print("\n1. 准备分布式训练数据")
print("-" * 30)

class ClassificationDataset(Dataset):
    """分类数据集"""
    
    def __init__(self, X, y):
        self.X = torch.FloatTensor(X)
        self.y = torch.LongTensor(y)
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]

# 生成分类数据
print("生成分类数据集...")
X, y = make_classification(
    n_samples=10000,
    n_features=50,
    n_informative=30,
    n_redundant=20,
    n_classes=5,
    random_state=42
)

# 数据标准化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# 划分训练集和验证集
X_train, X_val, y_train, y_val = train_test_split(
    X_scaled, y, test_size=0.2, random_state=42, stratify=y
)

print(f"训练集大小: {X_train.shape}")
print(f"验证集大小: {X_val.shape}")
print(f"特征维度: {X_train.shape[1]}")
print(f"类别数量: {len(np.unique(y))}")

# 2. 定义分布式训练模型
print("\n2. 定义分布式训练模型")
print("-" * 30)

class DistributedClassifier(nn.Module):
    """分布式分类器"""
    
    def __init__(self, input_size, hidden_sizes, num_classes, dropout_rate=0.3):
        super(DistributedClassifier, self).__init__()
        
        layers = []
        prev_size = input_size
        
        # 构建隐藏层
        for hidden_size in hidden_sizes:
            layers.extend([
                nn.Linear(prev_size, hidden_size),
                nn.BatchNorm1d(hidden_size),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ])
            prev_size = hidden_size
        
        # 输出层
        layers.append(nn.Linear(prev_size, num_classes))
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.network(x)

# 3. Ray分布式训练Worker
print("\n3. Ray分布式训练Worker")
print("-" * 30)

@ray.remote(num_cpus=1)
class DistributedTrainingWorker:
    """分布式训练Worker"""
    
    def __init__(self, worker_id: int, model_config: Dict[str, Any], 
                 train_config: Dict[str, Any]):
        self.worker_id = worker_id
        self.model_config = model_config
        self.train_config = train_config
        
        # 创建模型
        self.model = DistributedClassifier(**model_config)
        
        # 创建优化器
        self.optimizer = optim.Adam(
            self.model.parameters(), 
            lr=train_config['learning_rate'],
            weight_decay=train_config['weight_decay']
        )
        
        # 损失函数
        self.criterion = nn.CrossEntropyLoss()
        
        # 训练历史
        self.train_history = []
        
        print(f"Worker {worker_id} 初始化完成")
    
    def set_model_weights(self, state_dict):
        """设置模型权重"""
        self.model.load_state_dict(state_dict)
    
    def get_model_weights(self):
        """获取模型权重"""
        return self.model.state_dict()
    
    def train_epoch(self, X_batch, y_batch):
        """训练一个epoch"""
        self.model.train()
        
        # 转换数据类型
        if isinstance(X_batch, np.ndarray):
            X_batch = torch.FloatTensor(X_batch)
            y_batch = torch.LongTensor(y_batch)
        
        total_loss = 0
        correct = 0
        total = 0
        
        # 分批训练
        batch_size = self.train_config['batch_size']
        num_batches = len(X_batch) // batch_size
        
        for i in range(num_batches):
            start_idx = i * batch_size
            end_idx = start_idx + batch_size
            
            batch_X = X_batch[start_idx:end_idx]
            batch_y = y_batch[start_idx:end_idx]
            
            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(batch_X)
            loss = self.criterion(outputs, batch_y)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += batch_y.size(0)
            correct += (predicted == batch_y).sum().item()
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0
        accuracy = 100 * correct / total if total > 0 else 0
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'worker_id': self.worker_id
        }
    
    def validate(self, X_val, y_val):
        """验证模型"""
        self.model.eval()
        
        if isinstance(X_val, np.ndarray):
            X_val = torch.FloatTensor(X_val)
            y_val = torch.LongTensor(y_val)
        
        with torch.no_grad():
            outputs = self.model(X_val)
            loss = self.criterion(outputs, y_val)
            _, predicted = torch.max(outputs.data, 1)
            accuracy = 100 * (predicted == y_val).sum().item() / len(y_val)
        
        return {
            'val_loss': loss.item(),
            'val_accuracy': accuracy,
            'worker_id': self.worker_id
        }

# 4. 参数服务器（用于同步模型权重）
print("\n4. 参数服务器")
print("-" * 30)

@ray.remote
class ParameterServer:
    """参数服务器，用于同步模型权重"""
    
    def __init__(self, model_config):
        self.model = DistributedClassifier(**model_config)
        self.version = 0
        print("参数服务器初始化完成")
    
    def get_weights(self):
        """获取当前模型权重"""
        return self.model.state_dict(), self.version
    
    def update_weights(self, gradients_list):
        """使用梯度更新权重（联邦平均）"""
        if not gradients_list:
            return
        
        # 简单的权重平均
        avg_state_dict = {}
        
        # 初始化平均权重
        for key in gradients_list[0].keys():
            avg_state_dict[key] = torch.zeros_like(gradients_list[0][key])
        
        # 计算平均权重
        for state_dict in gradients_list:
            for key in state_dict.keys():
                avg_state_dict[key] += state_dict[key]
        
        for key in avg_state_dict.keys():
            avg_state_dict[key] /= len(gradients_list)
        
        # 更新模型权重
        self.model.load_state_dict(avg_state_dict)
        self.version += 1
        
        return self.version
    
    def save_checkpoint(self, filepath):
        """保存检查点"""
        checkpoint = {
            'model_state_dict': self.model.state_dict(),
            'version': self.version
        }
        torch.save(checkpoint, filepath)
        return filepath

# 5. 分布式训练协调器
print("\n5. 分布式训练协调器")
print("-" * 30)

class DistributedTrainingCoordinator:
    """分布式训练协调器"""
    
    def __init__(self, num_workers=4):
        self.num_workers = num_workers
        self.workers = []
        self.parameter_server = None
        
        # 模型配置
        self.model_config = {
            'input_size': X_train.shape[1],
            'hidden_sizes': [128, 64, 32],
            'num_classes': len(np.unique(y)),
            'dropout_rate': 0.3
        }
        
        # 训练配置
        self.train_config = {
            'learning_rate': 0.001,
            'weight_decay': 1e-4,
            'batch_size': 64
        }
        
        print(f"分布式训练协调器初始化，{num_workers}个worker")
    
    def setup_workers(self):
        """设置workers和参数服务器"""
        # 创建参数服务器
        self.parameter_server = ParameterServer.remote(self.model_config)
        
        # 创建workers
        for i in range(self.num_workers):
            worker = DistributedTrainingWorker.remote(
                worker_id=i,
                model_config=self.model_config,
                train_config=self.train_config
            )
            self.workers.append(worker)
        
        print(f"创建了{len(self.workers)}个训练worker")
    
    def distribute_data(self, X_train, y_train):
        """分发训练数据到各个worker"""
        chunk_size = len(X_train) // self.num_workers
        data_chunks = []
        
        for i in range(self.num_workers):
            start_idx = i * chunk_size
            end_idx = start_idx + chunk_size if i < self.num_workers - 1 else len(X_train)
            
            X_chunk = X_train[start_idx:end_idx]
            y_chunk = y_train[start_idx:end_idx]
            data_chunks.append((X_chunk, y_chunk))
        
        print(f"数据分发完成，每个worker约{chunk_size}个样本")
        return data_chunks
    
    def train(self, X_train, y_train, X_val, y_val, epochs=20):
        """执行分布式训练"""
        print(f"\n开始分布式训练，{epochs}个epoch")
        
        # 分发数据
        data_chunks = self.distribute_data(X_train, y_train)
        
        # 训练历史
        history = {
            'train_loss': [],
            'train_accuracy': [],
            'val_loss': [],
            'val_accuracy': []
        }
        
        for epoch in range(epochs):
            epoch_start_time = time.time()
            
            # 1. 获取当前模型权重
            weights, version = ray.get(self.parameter_server.get_weights.remote())
            
            # 2. 分发权重到所有worker
            weight_futures = []
            for worker in self.workers:
                future = worker.set_model_weights.remote(weights)
                weight_futures.append(future)
            ray.get(weight_futures)  # 等待权重分发完成
            
            # 3. 并行训练
            train_futures = []
            for i, worker in enumerate(self.workers):
                X_chunk, y_chunk = data_chunks[i]
                future = worker.train_epoch.remote(X_chunk, y_chunk)
                train_futures.append(future)
            
            # 4. 收集训练结果
            train_results = ray.get(train_futures)
            
            # 5. 获取更新后的权重
            weight_futures = []
            for worker in self.workers:
                future = worker.get_model_weights.remote()
                weight_futures.append(future)
            updated_weights = ray.get(weight_futures)
            
            # 6. 更新参数服务器
            new_version = ray.get(
                self.parameter_server.update_weights.remote(updated_weights)
            )
            
            # 7. 验证（使用第一个worker）
            val_result = ray.get(
                self.workers[0].validate.remote(X_val, y_val)
            )
            
            # 8. 统计结果
            avg_train_loss = np.mean([r['loss'] for r in train_results])
            avg_train_acc = np.mean([r['accuracy'] for r in train_results])
            
            history['train_loss'].append(avg_train_loss)
            history['train_accuracy'].append(avg_train_acc)
            history['val_loss'].append(val_result['val_loss'])
            history['val_accuracy'].append(val_result['val_accuracy'])
            
            epoch_time = time.time() - epoch_start_time
            
            # 打印进度
            if epoch % 5 == 0 or epoch == epochs - 1:
                print(f"Epoch {epoch+1:3d}/{epochs}: "
                      f"Train Loss={avg_train_loss:.4f}, "
                      f"Train Acc={avg_train_acc:.2f}%, "
                      f"Val Loss={val_result['val_loss']:.4f}, "
                      f"Val Acc={val_result['val_accuracy']:.2f}%, "
                      f"Time={epoch_time:.2f}s")
        
        return history
    
    def save_model(self, filepath):
        """保存训练好的模型"""
        checkpoint_path = ray.get(
            self.parameter_server.save_checkpoint.remote(filepath)
        )
        print(f"模型已保存到: {checkpoint_path}")
        return checkpoint_path

# 6. 执行分布式训练
print("\n6. 执行分布式训练")
print("-" * 30)

# 创建训练协调器
coordinator = DistributedTrainingCoordinator(num_workers=4)

# 设置workers
coordinator.setup_workers()

# 开始训练
start_time = time.time()
training_history = coordinator.train(
    X_train, y_train, X_val, y_val, epochs=20
)
total_training_time = time.time() - start_time

print(f"\n分布式训练完成！")
print(f"总训练时间: {total_training_time:.2f}秒")
print(f"最终训练准确率: {training_history['train_accuracy'][-1]:.2f}%")
print(f"最终验证准确率: {training_history['val_accuracy'][-1]:.2f}%")

# 保存模型
model_path = "distributed_model.pth"
coordinator.save_model(model_path)

# 7. 性能对比：单机 vs 分布式
print("\n7. 性能对比：单机 vs 分布式")
print("-" * 30)

def single_machine_training(X_train, y_train, X_val, y_val, epochs=20):
    """单机训练对比"""
    model = DistributedClassifier(**coordinator.model_config)
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    criterion = nn.CrossEntropyLoss()
    
    # 创建数据加载器
    train_dataset = ClassificationDataset(X_train, y_train)
    train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
    
    start_time = time.time()
    
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        correct = 0
        total = 0
        
        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total += batch_y.size(0)
            correct += (predicted == batch_y).sum().item()
    
    single_time = time.time() - start_time
    return single_time

print("运行单机训练对比...")
single_training_time = single_machine_training(X_train, y_train, X_val, y_val, epochs=20)

print(f"单机训练时间: {single_training_time:.2f}秒")
print(f"分布式训练时间: {total_training_time:.2f}秒")
print(f"加速比: {single_training_time / total_training_time:.2f}x")

# 8. 训练监控和可视化
print("\n8. 训练监控和可视化")
print("-" * 30)

def plot_training_history(history):
    """绘制训练历史"""
    try:
        import matplotlib.pyplot as plt
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # 损失曲线
        ax1.plot(history['train_loss'], label='训练损失', color='blue')
        ax1.plot(history['val_loss'], label='验证损失', color='red')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('损失')
        ax1.set_title('分布式训练损失曲线')
        ax1.legend()
        ax1.grid(True)
        
        # 准确率曲线
        ax2.plot(history['train_accuracy'], label='训练准确率', color='blue')
        ax2.plot(history['val_accuracy'], label='验证准确率', color='red')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('准确率 (%)')
        ax2.set_title('分布式训练准确率曲线')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig('ray/distributed_training_history.png', dpi=150, bbox_inches='tight')
        print("训练历史图已保存为 'distributed_training_history.png'")
        
    except ImportError:
        print("matplotlib未安装，跳过可视化")

plot_training_history(training_history)

# 9. 分布式训练最佳实践
print("\n9. 分布式训练最佳实践")
print("-" * 30)

best_practices = """
Ray分布式训练最佳实践：

1. 数据分布策略：
   - 数据并行：每个worker处理不同的数据子集
   - 确保数据分布均匀，避免数据倾斜
   - 使用DistributedSampler保证数据不重复

2. 模型同步策略：
   - 同步SGD：所有worker同步更新参数
   - 异步SGD：worker异步更新参数（可能不稳定）
   - 联邦平均：定期平均所有worker的模型权重

3. 通信优化：
   - 使用ray.put()共享大对象
   - 减少参数传输频率
   - 压缩梯度或使用梯度量化

4. 容错处理：
   - 定期保存检查点
   - 监控worker状态
   - 实现自动重启机制

5. 资源管理：
   - 合理分配CPU/GPU资源
   - 监控内存使用
   - 使用placement groups控制任务放置

6. 调试和监控：
   - 记录详细的训练日志
   - 监控收敛性和稳定性
   - 使用Ray Dashboard查看集群状态
"""

print(best_practices)

# 10. 清理资源
print("\n10. 训练总结")
print("-" * 30)

print(f"""
分布式训练总结：

配置信息：
- Worker数量: {coordinator.num_workers}
- 数据集大小: {len(X_train)} (训练) + {len(X_val)} (验证)
- 模型参数: {sum(p.numel() for p in coordinator.parameter_server.model.parameters() if hasattr(coordinator.parameter_server, 'model'))}
- 训练轮数: 20

性能结果：
- 分布式训练时间: {total_training_time:.2f}秒
- 单机训练时间: {single_training_time:.2f}秒
- 加速比: {single_training_time / total_training_time:.2f}x
- 最终验证准确率: {training_history['val_accuracy'][-1]:.2f}%

输出文件：
- 模型检查点: {model_path}
- 训练历史图: distributed_training_history.png

下一步学习：
- 运行 04_ray_embedding_service.py 学习推理服务
""")

print("\n" + "=" * 50)
print("Ray分布式训练实战完成！")
print("接下来请运行 04_ray_embedding_service.py")
print("=" * 50)
