#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ray基础API学习 - 第一部分
包含：Ray初始化、远程函数、远程类、并行计算等核心概念
"""

import ray
import time
import numpy as np
from typing import List
import psutil
import os

print("=" * 50)
print("Ray基础API学习")
print("=" * 50)

# 1. Ray简介和初始化
print("\n1. Ray简介和初始化")
print("-" * 30)

print("""
Ray是什么？
- 分布式计算框架：可以轻松地将Python程序扩展到多核或多机
- 统一接口：提供简单的API来并行化Python函数和类
- 高性能：基于共享内存和零拷贝序列化
- 容错性：自动处理节点故障和任务重试
- 生态丰富：包含机器学习、强化学习、超参数调优等工具

Ray vs 传统并行计算：
- 比multiprocessing更高效
- 比Celery更简单
- 比Spark更灵活
- 专为机器学习优化
""")

# 检查Ray是否已安装
try:
    import ray
    print(f"Ray版本: {ray.__version__}")
except ImportError:
    print("Ray未安装，请运行: pip install ray[default]")
    exit(1)

# 初始化Ray
# 如果已经初始化过，先关闭
if ray.is_initialized():
    ray.shutdown()

# 初始化Ray集群
ray.init(
    num_cpus=4,  # 指定CPU核心数
    num_gpus=0,  # 指定GPU数量
    ignore_reinit_error=True  # 忽略重复初始化错误
)

print(f"\nRay集群信息:")
print(f"节点数量: {len(ray.nodes())}")
print(f"可用CPU: {ray.cluster_resources().get('CPU', 0)}")
print(f"可用GPU: {ray.cluster_resources().get('GPU', 0)}")
print(f"可用内存: {ray.cluster_resources().get('memory', 0) / 1e9:.1f} GB")

# 2. 远程函数 (@ray.remote)
print("\n2. 远程函数 (@ray.remote)")
print("-" * 30)

# 普通函数
def normal_square(x):
    """普通的平方函数"""
    time.sleep(1)  # 模拟计算时间
    return x ** 2

# 远程函数
@ray.remote
def remote_square(x):
    """远程平方函数"""
    time.sleep(1)  # 模拟计算时间
    return x ** 2

# 比较执行时间
print("比较普通函数vs远程函数的执行时间:")

# 串行执行普通函数
start_time = time.time()
normal_results = []
for i in range(4):
    result = normal_square(i)
    normal_results.append(result)
normal_time = time.time() - start_time

print(f"串行执行4次普通函数: {normal_time:.2f}秒")
print(f"结果: {normal_results}")

# 并行执行远程函数
start_time = time.time()
# 提交任务（非阻塞）
futures = []
for i in range(4):
    future = remote_square.remote(i)  # 返回ObjectRef
    futures.append(future)

# 获取结果（阻塞）
remote_results = ray.get(futures)
remote_time = time.time() - start_time

print(f"并行执行4次远程函数: {remote_time:.2f}秒")
print(f"结果: {remote_results}")
print(f"加速比: {normal_time / remote_time:.2f}x")

# 3. ObjectRef和ray.get()
print("\n3. ObjectRef和ray.get()")
print("-" * 30)

@ray.remote
def slow_function(x):
    """慢函数，用于演示异步执行"""
    time.sleep(2)
    return f"处理完成: {x}"

print("演示异步执行:")
print("提交任务...")
future1 = slow_function.remote("任务1")
future2 = slow_function.remote("任务2")

print(f"future1类型: {type(future1)}")
print(f"future1值: {future1}")

print("可以在等待结果的同时做其他事情...")
time.sleep(1)
print("做了一些其他工作...")

print("现在获取结果:")
result1 = ray.get(future1)
result2 = ray.get(future2)
print(f"结果1: {result1}")
print(f"结果2: {result2}")

# 4. 远程类 (@ray.remote class)
print("\n4. 远程类 (@ray.remote class)")
print("-" * 30)

@ray.remote
class Counter:
    """远程计数器类"""
    
    def __init__(self, initial_value=0):
        self.value = initial_value
        print(f"计数器初始化，初始值: {initial_value}")
    
    def increment(self, delta=1):
        """增加计数"""
        self.value += delta
        return self.value
    
    def get_value(self):
        """获取当前值"""
        return self.value
    
    def reset(self):
        """重置计数器"""
        self.value = 0
        return self.value

# 创建远程类实例
print("创建远程计数器实例:")
counter = Counter.remote(10)  # 返回ActorHandle

# 调用远程方法
print("调用远程方法:")
future1 = counter.increment.remote(5)
future2 = counter.increment.remote(3)
future3 = counter.get_value.remote()

# 获取结果
result1 = ray.get(future1)
result2 = ray.get(future2)
result3 = ray.get(future3)

print(f"第一次增加5: {result1}")
print(f"第二次增加3: {result2}")
print(f"最终值: {result3}")

# 5. 并行数据处理
print("\n5. 并行数据处理")
print("-" * 30)

@ray.remote
def process_chunk(data_chunk):
    """处理数据块"""
    # 模拟复杂的数据处理
    result = []
    for item in data_chunk:
        # 模拟计算密集型操作
        processed = sum(i**2 for i in range(item, item + 100))
        result.append(processed)
    return result

# 生成测试数据
data = list(range(1000))
chunk_size = 250

print(f"处理{len(data)}个数据点，分成{len(data)//chunk_size}个块")

# 串行处理
start_time = time.time()
serial_results = []
for i in range(0, len(data), chunk_size):
    chunk = data[i:i + chunk_size]
    chunk_result = []
    for item in chunk:
        processed = sum(i**2 for i in range(item, item + 100))
        chunk_result.append(processed)
    serial_results.extend(chunk_result)
serial_time = time.time() - start_time

# 并行处理
start_time = time.time()
futures = []
for i in range(0, len(data), chunk_size):
    chunk = data[i:i + chunk_size]
    future = process_chunk.remote(chunk)
    futures.append(future)

parallel_results = []
chunk_results = ray.get(futures)
for chunk_result in chunk_results:
    parallel_results.extend(chunk_result)
parallel_time = time.time() - start_time

print(f"串行处理时间: {serial_time:.2f}秒")
print(f"并行处理时间: {parallel_time:.2f}秒")
print(f"加速比: {serial_time / parallel_time:.2f}x")
print(f"结果一致性: {serial_results == parallel_results}")

# 6. 资源管理
print("\n6. 资源管理")
print("-" * 30)

@ray.remote(num_cpus=2)  # 指定需要2个CPU核心
def cpu_intensive_task(n):
    """CPU密集型任务"""
    result = 0
    for i in range(n):
        result += i ** 2
    return result

@ray.remote(num_cpus=0.5)  # 指定需要0.5个CPU核心
def light_task(x):
    """轻量级任务"""
    return x * 2

print("提交不同资源需求的任务:")
heavy_future = cpu_intensive_task.remote(1000000)
light_futures = [light_task.remote(i) for i in range(8)]

print("等待任务完成...")
heavy_result = ray.get(heavy_future)
light_results = ray.get(light_futures)

print(f"CPU密集型任务结果: {heavy_result}")
print(f"轻量级任务结果: {light_results}")

# 7. 错误处理和容错
print("\n7. 错误处理和容错")
print("-" * 30)

@ray.remote
def may_fail_task(x):
    """可能失败的任务"""
    if x == 3:
        raise ValueError(f"任务{x}故意失败")
    return x ** 2

print("提交可能失败的任务:")
futures = [may_fail_task.remote(i) for i in range(5)]

print("处理结果和异常:")
for i, future in enumerate(futures):
    try:
        result = ray.get(future)
        print(f"任务{i}成功: {result}")
    except Exception as e:
        print(f"任务{i}失败: {e}")

# 8. 实际应用示例：蒙特卡洛估算π
print("\n8. 实际应用示例：蒙特卡洛估算π")
print("-" * 30)

@ray.remote
def estimate_pi_chunk(num_samples):
    """蒙特卡洛方法估算π的一部分"""
    count_inside = 0
    for _ in range(num_samples):
        x = np.random.random()
        y = np.random.random()
        if x**2 + y**2 <= 1:
            count_inside += 1
    return count_inside

def estimate_pi_parallel(total_samples, num_workers=4):
    """并行估算π"""
    samples_per_worker = total_samples // num_workers
    
    # 提交任务
    futures = []
    for _ in range(num_workers):
        future = estimate_pi_chunk.remote(samples_per_worker)
        futures.append(future)
    
    # 收集结果
    results = ray.get(futures)
    total_inside = sum(results)
    
    # 计算π的估值
    pi_estimate = 4 * total_inside / total_samples
    return pi_estimate

# 运行蒙特卡洛估算
total_samples = 1000000
print(f"使用{total_samples}个样本估算π:")

start_time = time.time()
pi_estimate = estimate_pi_parallel(total_samples)
elapsed_time = time.time() - start_time

print(f"π的估算值: {pi_estimate:.6f}")
print(f"真实π值: {np.pi:.6f}")
print(f"误差: {abs(pi_estimate - np.pi):.6f}")
print(f"计算时间: {elapsed_time:.2f}秒")

# 9. 监控和调试
print("\n9. 监控和调试")
print("-" * 30)

# 获取集群状态
print("集群状态:")
print(f"活跃任务数: {len(ray.worker.global_worker.core_worker.get_all_reference_counts())}")

# 获取对象存储信息
object_store_stats = ray.memory_summary(stats_only=True)

print("对象存储统计:")
print(f"已用内存: {object_store_stats.get('object_store_used_memory', 0) / 1e6:.1f} MB")
print(f"总内存: {object_store_stats.get('object_store_total_memory', 0) / 1e6:.1f} MB")

# 10. 清理资源
print("\n10. 清理资源")
print("-" * 30)

print("Ray学习完成，清理资源...")
# 注意：在实际应用中，通常在程序结束时调用ray.shutdown()
# 这里为了演示目的暂时不关闭，因为后续文件可能还需要使用Ray

print(f"""
Ray基础概念总结:
1. @ray.remote 装饰器：将函数或类转换为远程执行
2. ObjectRef：异步任务的引用，类似于Future
3. ray.get()：获取远程任务的结果（阻塞操作）
4. ray.put()：将大对象放入对象存储（避免重复序列化）
5. 资源管理：通过num_cpus、num_gpus等参数控制资源分配
6. 容错性：自动处理任务失败和节点故障

下一步学习：
- 运行 02_ray_pytorch_comparison.py 了解Ray与PyTorch的结合
""")

print("\n" + "=" * 50)
print("Ray基础API学习完成！")
print("接下来请运行 02_ray_pytorch_comparison.py")
print("=" * 50)
