#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ray基础API学习 - 第一部分
包含：Ray初始化、远程函数、远程类、并行计算等核心概念
"""

import ray
import time
import numpy as np
from typing import List
import psutil
import os

print("=" * 50)
print("Ray基础API学习")
print("=" * 50)

# 1. Ray简介和初始化
print("\n1. Ray简介和初始化")
print("-" * 30)

print("""
Ray是什么？
- 分布式计算框架：可以轻松地将Python程序扩展到多核或多机
- 统一接口：提供简单的API来并行化Python函数和类
- 高性能：基于共享内存和零拷贝序列化
- 容错性：自动处理节点故障和任务重试
- 生态丰富：包含机器学习、强化学习、超参数调优等工具

Ray vs 传统并行计算：
- 比multiprocessing更高效
- 比Celery更简单
- 比Spark更灵活
- 专为机器学习优化
""")

# 检查Ray是否已安装
try:
    import ray
    print(f"Ray版本: {ray.__version__}")
except ImportError:
    print("Ray未安装，请运行: pip install ray[default]")
    exit(1)

# 初始化Ray
# 如果已经初始化过，先关闭
if ray.is_initialized():
    ray.shutdown()

# 初始化Ray集群
ray.init(
    num_cpus=4,  # 指定CPU核心数
    num_gpus=0,  # 指定GPU数量
    ignore_reinit_error=True  # 忽略重复初始化错误
)

print(f"\nRay集群信息:")
print(f"节点数量: {len(ray.nodes())}")
print(f"可用CPU: {ray.cluster_resources().get('CPU', 0)}")
print(f"可用GPU: {ray.cluster_resources().get('GPU', 0)}")
print(f"可用内存: {ray.cluster_resources().get('memory', 0) / 1e9:.1f} GB")
print("version" , ray.__version__)


# 2. 远程函数 (@ray.remote)
print("\n2. 远程函数 (@ray.remote)")
print("-" * 30)

# 普通函数
def normal_square(x):
    """普通的平方函数"""
    time.sleep(1)  # 模拟计算时间
    return x ** 2

# 远程函数
@ray.remote
def remote_square(x):
    """远程平方函数"""
    time.sleep(1)  # 模拟计算时间
    return x ** 2

# 比较执行时间
print("比较普通函数vs远程函数的执行时间:")

# 串行执行普通函数
start_time = time.time()
normal_results = []
for i in range(4):
    result = normal_square(i)
    normal_results.append(result)
normal_time = time.time() - start_time

print(f"串行执行4次普通函数: {normal_time:.2f}秒")
print(f"结果: {normal_results}")

# 并行执行远程函数
start_time = time.time()
# 提交任务（非阻塞）
futures = []
for i in range(4):
    future = remote_square.remote(i)  # 返回ObjectRef
    futures.append(future)

# 获取结果（阻塞）
remote_results = ray.get(futures)
remote_time = time.time() - start_time

print(f"并行执行4次远程函数: {remote_time:.2f}秒")
print(f"结果: {remote_results}")
print(f"加速比: {normal_time / remote_time:.2f}x")

# 3. ObjectRef和ray.get()
print("\n3. ObjectRef和ray.get()")
print("-" * 30)

@ray.remote
def slow_function(x):
    """慢函数，用于演示异步执行"""
    time.sleep(2)
    return f"处理完成: {x}"

print("演示异步执行:")
print("提交任务...")
future1 = slow_function.remote("任务1")
future2 = slow_function.remote("任务2")

print(f"future1类型: {type(future1)}")
print(f"future1值: {future1}")

print("可以在等待结果的同时做其他事情...")
time.sleep(1)
print("做了一些其他工作...")

print("现在获取结果:")
result1 = ray.get(future1)
result2 = ray.get(future2)
print(f"结果1: {result1}")
print(f"结果2: {result2}")

# 4. 远程类 (@ray.remote class)
print("\n4. 远程类 (@ray.remote class)")
print("-" * 30)

@ray.remote
class Counter:
    """远程计数器类"""
    
    def __init__(self, initial_value=0):
        self.value = initial_value
        print(f"计数器初始化，初始值: {initial_value}")
    
    def increment(self, delta=1):
        """增加计数"""
        self.value += delta
        return self.value
    
    def get_value(self):
        """获取当前值"""
        return self.value
    
    def reset(self):
        """重置计数器"""
        self.value = 0
        return self.value

# 创建远程类实例
print("创建远程计数器实例:")
counter = Counter.remote(10)  # 返回ActorHandle

# 调用远程方法
print("调用远程方法:")
future1 = counter.increment.remote(5)
future2 = counter.increment.remote(3)
future3 = counter.get_value.remote()

# 获取结果
result1 = ray.get(future1)
result2 = ray.get(future2)
result3 = ray.get(future3)

print(f"第一次增加5: {result1}")
print(f"第二次增加3: {result2}")
print(f"最终值: {result3}")

# 5. 并行数据处理
print("\n5. 并行数据处理")
print("-" * 30)

@ray.remote
def process_chunk(data_chunk):
    """处理数据块"""
    # 模拟复杂的数据处理
    result = []
    for item in data_chunk:
        # 模拟计算密集型操作
        processed = sum(i**2 for i in range(item, item + 100))
        result.append(processed)
    return result

# 生成测试数据
data = list(range(1000))
chunk_size = 250

print(f"处理{len(data)}个数据点，分成{len(data)//chunk_size}个块")

# 串行处理
start_time = time.time()
serial_results = []
for i in range(0, len(data), chunk_size):
    chunk = data[i:i + chunk_size]
    chunk_result = []
    for item in chunk:
        processed = sum(i**2 for i in range(item, item + 100))
        chunk_result.append(processed)
    serial_results.extend(chunk_result)
serial_time = time.time() - start_time

# 并行处理
start_time = time.time()
futures = []
for i in range(0, len(data), chunk_size):
    chunk = data[i:i + chunk_size]
    future = process_chunk.remote(chunk)
    futures.append(future)

parallel_results = []
chunk_results = ray.get(futures)
for chunk_result in chunk_results:
    parallel_results.extend(chunk_result)
parallel_time = time.time() - start_time

print(f"串行处理时间: {serial_time:.2f}秒")
print(f"并行处理时间: {parallel_time:.2f}秒")
print(f"加速比: {serial_time / parallel_time:.2f}x")
print(f"结果一致性: {serial_results == parallel_results}")

# 6. 资源管理
print("\n6. 资源管理")
print("-" * 30)

@ray.remote(num_cpus=2)  # 指定需要2个CPU核心
def cpu_intensive_task(n):
    """CPU密集型任务"""
    result = 0
    for i in range(n):
        result += i ** 2
    return result

@ray.remote(num_cpus=0.5)  # 指定需要0.5个CPU核心
def light_task(x):
    """轻量级任务"""
    return x * 2

print("提交不同资源需求的任务:")
heavy_future = cpu_intensive_task.remote(1000000)
light_futures = [light_task.remote(i) for i in range(8)]

print("等待任务完成...")
heavy_result = ray.get(heavy_future)
light_results = ray.get(light_futures)

print(f"CPU密集型任务结果: {heavy_result}")
print(f"轻量级任务结果: {light_results}")

# 7. 错误处理和容错
print("\n7. 错误处理和容错")
print("-" * 30)

@ray.remote
def may_fail_task(x):
    """可能失败的任务"""
    if x == 3:
        raise ValueError(f"任务{x}故意失败")
    return x ** 2

print("提交可能失败的任务:")
futures = [may_fail_task.remote(i) for i in range(5)]

print("处理结果和异常:")
for i, future in enumerate(futures):
    try:
        result = ray.get(future)
        print(f"任务{i}成功: {result}")
    except Exception as e:
        print(f"任务{i}失败: {e}")

# 8. 实际应用示例：蒙特卡洛估算π
print("\n8. 实际应用示例：蒙特卡洛估算π")
print("-" * 30)

@ray.remote
def estimate_pi_chunk(num_samples):
    """蒙特卡洛方法估算π的一部分"""
    count_inside = 0
    for _ in range(num_samples):
        x = np.random.random()
        y = np.random.random()
        if x**2 + y**2 <= 1:
            count_inside += 1
    return count_inside

def estimate_pi_parallel(total_samples, num_workers=4):
    """并行估算π"""
    samples_per_worker = total_samples // num_workers
    
    # 提交任务
    futures = []
    for _ in range(num_workers):
        future = estimate_pi_chunk.remote(samples_per_worker)
        futures.append(future)
    
    # 收集结果
    results = ray.get(futures)
    total_inside = sum(results)
    
    # 计算π的估值
    pi_estimate = 4 * total_inside / total_samples
    return pi_estimate

# 运行蒙特卡洛估算
total_samples = 1000000
print(f"使用{total_samples}个样本估算π:")

start_time = time.time()
pi_estimate = estimate_pi_parallel(total_samples)
elapsed_time = time.time() - start_time

print(f"π的估算值: {pi_estimate:.6f}")
print(f"真实π值: {np.pi:.6f}")
print(f"误差: {abs(pi_estimate - np.pi):.6f}")
print(f"计算时间: {elapsed_time:.2f}秒")

# 9. 监控和调试
print("\n9. 监控和调试")
print("-" * 30)

# 获取集群状态
print("集群状态:")
print(f"活跃任务数: {len(ray.worker.global_worker.core_worker.get_all_reference_counts())}")

# 10. Ray Core API深入
print("\n10. Ray Core API深入")
print("-" * 30)

print("""
Ray Core API架构：

1. Task API (无状态)：
   - @ray.remote 装饰函数
   - 适合无状态计算
   - 自动负载均衡
   - 容错重试

2. Actor API (有状态)：
   - @ray.remote 装饰类
   - 维护状态
   - 方法调用
   - 生命周期管理

3. Object Store：
   - 共享内存存储
   - 零拷贝传输
   - 自动垃圾回收
   - 分布式缓存
""")

# Task vs Actor 详细对比
@ray.remote
def stateless_task(x):
    """无状态任务：每次调用都是独立的"""
    import os
    pid = os.getpid()
    return f"Task processed {x} in process {pid}"

@ray.remote
class StatefulActor:
    """有状态Actor：维护内部状态"""

    def __init__(self, initial_value=0):
        import os
        self.value = initial_value
        self.call_count = 0
        self.pid = os.getpid()
        print(f"Actor initialized in process {self.pid}")

    def increment(self, delta=1):
        self.call_count += 1
        self.value += delta
        return {
            "value": self.value,
            "call_count": self.call_count,
            "pid": self.pid
        }

    def get_state(self):
        return {
            "value": self.value,
            "call_count": self.call_count,
            "pid": self.pid
        }

print("\nTask vs Actor 对比:")

# 测试无状态任务
print("无状态任务测试:")
task_futures = [stateless_task.remote(i) for i in range(3)]
task_results = ray.get(task_futures)
for result in task_results:
    print(f"  {result}")

# 测试有状态Actor
print("\n有状态Actor测试:")
actor = StatefulActor.remote(100)

# 多次调用同一个Actor
actor_futures = [actor.increment.remote(i) for i in range(3)]
actor_results = ray.get(actor_futures)
for result in actor_results:
    print(f"  {result}")

# 11. Object Store 和 ray.put/ray.get
print("\n11. Object Store 和 ray.put/ray.get")
print("-" * 30)

# 创建大对象
large_data = np.random.randn(1000, 1000)  # 8MB的数组
print(f"大对象大小: {large_data.nbytes / 1024 / 1024:.1f} MB")

# 方法1：直接传递（低效）
@ray.remote
def process_data_direct(data):
    return np.sum(data)

print("\n方法1：直接传递大对象")
start_time = time.time()
futures = [process_data_direct.remote(large_data) for _ in range(4)]
results = ray.get(futures)
direct_time = time.time() - start_time
print(f"直接传递时间: {direct_time:.3f}秒")

# 方法2：使用ray.put()（高效）
print("\n方法2：使用ray.put()共享对象")
start_time = time.time()
data_ref = ray.put(large_data)  # 放入对象存储
futures = [process_data_direct.remote(data_ref) for _ in range(4)]
results = ray.get(futures)
put_time = time.time() - start_time
print(f"ray.put()时间: {put_time:.3f}秒")
print(f"性能提升: {direct_time / put_time:.2f}x")

# 查看ObjectRef信息
print(f"\nObjectRef信息:")
print(f"类型: {type(data_ref)}")
print(f"ObjectRef ID: {data_ref}")

# 12. 生命周期管理
print("\n12. Actor生命周期管理")
print("-" * 30)

@ray.remote
class ManagedActor:
    """演示Actor生命周期管理"""

    def __init__(self, name):
        self.name = name
        self.start_time = time.time()
        print(f"Actor {name} 启动")

    def get_uptime(self):
        return time.time() - self.start_time

    def cleanup(self):
        print(f"Actor {self.name} 清理资源")
        return f"Actor {self.name} 已清理"

    def __del__(self):
        print(f"Actor {self.name} 被销毁")

# 创建和管理Actor
print("创建Actor:")
managed_actor = ManagedActor.remote("TestActor")

# 使用Actor
uptime = ray.get(managed_actor.get_uptime.remote())
print(f"Actor运行时间: {uptime:.2f}秒")

# 手动清理
cleanup_result = ray.get(managed_actor.cleanup.remote())
print(f"清理结果: {cleanup_result}")

# 删除Actor引用（触发垃圾回收）
del managed_actor

# 13. 高级资源管理
print("\n13. 高级资源管理")
print("-" * 30)

# 自定义资源
@ray.remote(resources={"custom_resource": 1})
def task_with_custom_resource():
    return "使用自定义资源的任务"

# GPU资源管理（如果有GPU）
@ray.remote(num_gpus=0.5)  # 使用半个GPU
def gpu_task():
    return "GPU任务"

# 内存资源管理
@ray.remote(memory=100 * 1024 * 1024)  # 100MB内存
def memory_intensive_task():
    return "内存密集型任务"

# Placement Groups（放置组）
print("Placement Groups示例:")
try:
    from ray.util.placement_group import placement_group, remove_placement_group

    # 创建放置组
    pg = placement_group([{"CPU": 2}, {"CPU": 2}], strategy="STRICT_PACK")
    ray.get(pg.ready())

    @ray.remote(num_cpus=1)
    def task_in_pg():
        import os
        return f"任务在进程 {os.getpid()} 中运行"

    # 在放置组中运行任务
    futures = [
        task_in_pg.options(placement_group=pg).remote()
        for _ in range(2)
    ]
    results = ray.get(futures)
    print("放置组任务结果:")
    for result in results:
        print(f"  {result}")

    # 清理放置组
    remove_placement_group(pg)

except Exception as e:
    print(f"放置组示例跳过: {e}")

# 14. 错误处理和重试机制
print("\n14. 错误处理和重试机制")
print("-" * 30)

@ray.remote(max_retries=3)  # 最多重试3次
def unreliable_task(fail_probability=0.7):
    """不可靠的任务，用于演示重试机制"""
    import random
    if random.random() < fail_probability:
        raise RuntimeError("任务随机失败")
    return "任务成功完成"

print("测试重试机制:")
try:
    # 提交多个可能失败的任务
    futures = [unreliable_task.remote(0.5) for _ in range(5)]
    results = ray.get(futures)
    print(f"成功完成 {len(results)} 个任务")
except Exception as e:
    print(f"部分任务失败: {e}")

# 15. 异步编程模式
print("\n15. 异步编程模式")
print("-" * 30)

@ray.remote
def async_task(task_id, duration):
    """异步任务"""
    time.sleep(duration)
    return f"任务 {task_id} 完成，耗时 {duration}秒"

print("异步编程模式演示:")

# 提交任务但不立即等待
print("提交异步任务...")
async_futures = [
    async_task.remote(i, np.random.uniform(0.5, 2.0))
    for i in range(3)
]

# 使用ray.wait()等待部分任务完成
print("等待任务完成...")
while async_futures:
    # 等待至少1个任务完成，最多等待1秒
    ready, not_ready = ray.wait(async_futures, num_returns=1, timeout=1.0)

    if ready:
        # 处理完成的任务
        results = ray.get(ready)
        for result in results:
            print(f"  {result}")

        # 更新待处理任务列表
        async_futures = not_ready
    else:
        print("  等待中...")

# 16. 性能监控和调试
print("\n16. 性能监控和调试")
print("-" * 30)

# 获取集群资源信息
cluster_resources = ray.cluster_resources()
print("集群资源:")
for resource, amount in cluster_resources.items():
    print(f"  {resource}: {amount}")

# 获取可用资源
available_resources = ray.available_resources()
print("\n可用资源:")
for resource, amount in available_resources.items():
    print(f"  {resource}: {amount}")

# 获取节点信息
nodes = ray.nodes()
print(f"\n集群节点数: {len(nodes)}")
for i, node in enumerate(nodes):
    print(f"  节点 {i}: {node.get('NodeManagerAddress', 'Unknown')}")

# 17. Ray核心概念总结
print("\n17. Ray核心概念总结")
print("-" * 30)

core_concepts = """
Ray Core API 核心概念：

1. Task (任务)：
   - 无状态函数调用
   - 自动负载均衡
   - 容错重试
   - 适合CPU密集型计算

2. Actor (角色)：
   - 有状态的远程类
   - 维护内部状态
   - 方法调用
   - 适合有状态服务

3. Object Store (对象存储)：
   - 分布式共享内存
   - 零拷贝数据传输
   - 自动垃圾回收
   - 高效数据共享

4. ObjectRef (对象引用)：
   - 异步计算的句柄
   - 延迟求值
   - 可传递和组合
   - 类似Future/Promise

5. 资源管理：
   - CPU/GPU/内存分配
   - 自定义资源类型
   - 放置组策略
   - 动态扩缩容

6. 容错机制：
   - 任务重试
   - 节点故障恢复
   - 对象重建
   - 血缘追踪

使用场景选择：
- 无状态计算 → Task
- 有状态服务 → Actor
- 大数据共享 → Object Store
- 异步编程 → ObjectRef
- 资源控制 → 资源管理
- 生产环境 → 容错机制
"""

print(core_concepts)

# 18. 清理资源
print("\n18. 清理资源")
print("-" * 30)

print("Ray基础学习完成，清理资源...")
# 注意：在实际应用中，通常在程序结束时调用ray.shutdown()
# 这里为了演示目的暂时不关闭，因为后续文件可能还需要使用Ray

print(f"""
Ray Core API 学习总结:

核心组件：
1. Task API - 无状态函数并行化
2. Actor API - 有状态类分布式化
3. Object Store - 高效数据共享
4. ObjectRef - 异步计算句柄
5. 资源管理 - 智能调度分配
6. 容错机制 - 生产级可靠性

关键优势：
- 简单易用的Python API
- 高性能分布式计算
- 强大的容错能力
- 灵活的资源管理
- 丰富的生态系统

下一步学习：
- 运行 02_ray_pytorch_comparison.py 了解Ray与PyTorch的结合
- 深入学习Ray Train、Ray Serve等高级组件
- 实践分布式机器学习项目
""")

print("\n" + "=" * 50)
print("Ray Core API深度学习完成！")
print("接下来请运行 02_ray_pytorch_comparison.py")
print("=" * 50)
