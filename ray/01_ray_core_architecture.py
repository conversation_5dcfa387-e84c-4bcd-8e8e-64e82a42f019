#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ray核心架构深度解析
包含：Ray架构组件、Task vs Actor详解、Object Store机制、调度器原理等
"""

import ray
import time
import numpy as np
import psutil
import os
from typing import List, Dict, Any
import threading
import multiprocessing

print("=" * 60)
print("Ray核心架构深度解析")
print("=" * 60)

# 确保Ray已初始化
if not ray.is_initialized():
    ray.init(ignore_reinit_error=True)

# 1. Ray架构概览
print("\n1. Ray架构概览")
print("-" * 40)

architecture_overview = """
Ray分布式系统架构：

┌─────────────────────────────────────────────────────────────┐
│                    Ray Cluster                              │
├─────────────────────────────────────────────────────────────┤
│  Head Node                    │  Worker Nodes               │
│  ┌─────────────────────────┐  │  ┌─────────────────────────┐ │
│  │ Global Control Store    │  │  │ Raylet (Node Manager)   │ │
│  │ - Cluster Metadata      │  │  │ - Local Scheduler       │ │
│  │ - Actor Registry        │  │  │ - Object Manager        │ │
│  │ - Task Dependencies     │  │  │ - Worker Processes      │ │
│  └─────────────────────────┘  │  └─────────────────────────┘ │
│  ┌─────────────────────────┐  │  ┌─────────────────────────┐ │
│  │ Global Scheduler        │  │  │ Object Store            │ │
│  │ - Task Placement        │  │  │ - Shared Memory         │ │
│  │ - Load Balancing        │  │  │ - Zero-copy Transfer    │ │
│  │ - Resource Management   │  │  │ - Automatic GC          │ │
│  └─────────────────────────┘  │  └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

核心组件说明：
1. Raylet: 每个节点的本地管理器
2. Object Store: 分布式共享内存系统
3. Global Control Store: 全局元数据存储
4. Global Scheduler: 全局任务调度器
5. Worker Process: 执行用户代码的进程
"""

print(architecture_overview)

# 2. Task详解
print("\n2. Task (任务) 详解")
print("-" * 40)

print("""
Task特性：
- 无状态：每次调用都是独立的
- 函数式：输入确定，输出确定
- 可重试：失败后可以重新执行
- 负载均衡：自动分配到空闲worker
- 并行执行：多个task可以同时运行
""")

@ray.remote
def cpu_intensive_task(n: int, task_id: str) -> Dict[str, Any]:
    """CPU密集型任务示例"""
    import os
    import time
    
    start_time = time.time()
    
    # 模拟CPU密集型计算
    result = 0
    for i in range(n):
        result += i ** 2
    
    end_time = time.time()
    
    return {
        "task_id": task_id,
        "result": result,
        "duration": end_time - start_time,
        "worker_pid": os.getpid(),
        "worker_node": ray.worker.global_worker.node_ip_address
    }

@ray.remote
def io_intensive_task(duration: float, task_id: str) -> Dict[str, Any]:
    """IO密集型任务示例"""
    import os
    import time
    
    start_time = time.time()
    
    # 模拟IO等待
    time.sleep(duration)
    
    end_time = time.time()
    
    return {
        "task_id": task_id,
        "duration": end_time - start_time,
        "worker_pid": os.getpid(),
        "worker_node": ray.worker.global_worker.node_ip_address
    }

# 测试Task并行执行
print("测试Task并行执行:")
print("提交CPU密集型任务...")

cpu_futures = [
    cpu_intensive_task.remote(100000, f"cpu_task_{i}") 
    for i in range(4)
]

print("提交IO密集型任务...")
io_futures = [
    io_intensive_task.remote(1.0, f"io_task_{i}") 
    for i in range(4)
]

# 收集结果
print("等待任务完成...")
cpu_results = ray.get(cpu_futures)
io_results = ray.get(io_futures)

print("\nCPU任务结果:")
for result in cpu_results:
    print(f"  {result['task_id']}: PID={result['worker_pid']}, "
          f"耗时={result['duration']:.3f}s")

print("\nIO任务结果:")
for result in io_results:
    print(f"  {result['task_id']}: PID={result['worker_pid']}, "
          f"耗时={result['duration']:.3f}s")

# 3. Actor详解
print("\n3. Actor (角色) 详解")
print("-" * 40)

print("""
Actor特性：
- 有状态：维护内部状态
- 单线程：方法调用串行执行
- 长生命周期：可以持续运行
- 方法调用：支持异步方法调用
- 资源绑定：可以绑定特定资源
""")

@ray.remote
class StatefulCounter:
    """有状态计数器Actor"""
    
    def __init__(self, initial_value: int = 0, name: str = "Counter"):
        self.value = initial_value
        self.name = name
        self.call_history = []
        self.worker_pid = os.getpid()
        self.start_time = time.time()
        
        print(f"Actor {name} 在进程 {self.worker_pid} 中启动")
    
    def increment(self, delta: int = 1) -> Dict[str, Any]:
        """增加计数"""
        old_value = self.value
        self.value += delta
        
        call_info = {
            "method": "increment",
            "old_value": old_value,
            "new_value": self.value,
            "delta": delta,
            "timestamp": time.time()
        }
        self.call_history.append(call_info)
        
        return call_info
    
    def decrement(self, delta: int = 1) -> Dict[str, Any]:
        """减少计数"""
        old_value = self.value
        self.value -= delta
        
        call_info = {
            "method": "decrement", 
            "old_value": old_value,
            "new_value": self.value,
            "delta": delta,
            "timestamp": time.time()
        }
        self.call_history.append(call_info)
        
        return call_info
    
    def get_state(self) -> Dict[str, Any]:
        """获取Actor状态"""
        return {
            "name": self.name,
            "current_value": self.value,
            "worker_pid": self.worker_pid,
            "uptime": time.time() - self.start_time,
            "total_calls": len(self.call_history),
            "call_history": self.call_history[-5:]  # 最近5次调用
        }
    
    def reset(self) -> Dict[str, Any]:
        """重置计数器"""
        old_value = self.value
        self.value = 0
        self.call_history.clear()
        
        return {
            "method": "reset",
            "old_value": old_value,
            "new_value": self.value
        }

# 测试Actor状态管理
print("测试Actor状态管理:")

# 创建多个Actor实例
actors = [
    StatefulCounter.remote(i * 10, f"Counter_{i}") 
    for i in range(3)
]

print("执行并发操作...")
# 对每个Actor执行不同操作
operations = []
for i, actor in enumerate(actors):
    operations.extend([
        actor.increment.remote(i + 1),
        actor.increment.remote(i + 2),
        actor.decrement.remote(1)
    ])

# 等待所有操作完成
operation_results = ray.get(operations)

# 获取每个Actor的最终状态
print("\nActor最终状态:")
for i, actor in enumerate(actors):
    state = ray.get(actor.get_state.remote())
    print(f"  {state['name']}: 值={state['current_value']}, "
          f"PID={state['worker_pid']}, 调用次数={state['total_calls']}")

# 4. Object Store深度解析
print("\n4. Object Store深度解析")
print("-" * 40)

print("""
Object Store特性：
- 共享内存：基于Apache Arrow的列式内存格式
- 零拷贝：进程间数据传输无需复制
- 分布式：跨节点透明访问
- 自动GC：引用计数和LRU淘汰
- 容错：通过血缘关系重建丢失对象
""")

def demonstrate_object_store():
    """演示Object Store机制"""
    
    # 创建不同大小的对象
    small_obj = np.random.randn(100, 100)      # ~80KB
    medium_obj = np.random.randn(1000, 1000)   # ~8MB  
    large_obj = np.random.randn(2000, 2000)    # ~32MB
    
    print(f"小对象大小: {small_obj.nbytes / 1024:.1f} KB")
    print(f"中对象大小: {medium_obj.nbytes / 1024 / 1024:.1f} MB")
    print(f"大对象大小: {large_obj.nbytes / 1024 / 1024:.1f} MB")
    
    # 测试不同传输方式的性能
    @ray.remote
    def process_object(obj):
        return np.sum(obj)
    
    # 方式1：直接传递（会序列化）
    print("\n方式1：直接传递对象")
    start_time = time.time()
    futures = [process_object.remote(large_obj) for _ in range(3)]
    results = ray.get(futures)
    direct_time = time.time() - start_time
    print(f"直接传递时间: {direct_time:.3f}秒")
    
    # 方式2：使用ray.put()
    print("\n方式2：使用ray.put()共享对象")
    start_time = time.time()
    obj_ref = ray.put(large_obj)
    futures = [process_object.remote(obj_ref) for _ in range(3)]
    results = ray.get(futures)
    put_time = time.time() - start_time
    print(f"ray.put()时间: {put_time:.3f}秒")
    print(f"性能提升: {direct_time / put_time:.2f}x")
    
    # 查看ObjectRef信息
    print(f"\nObjectRef信息:")
    print(f"ObjectRef: {obj_ref}")
    print(f"对象大小: {ray.get(obj_ref).nbytes / 1024 / 1024:.1f} MB")
    
    return obj_ref

obj_ref = demonstrate_object_store()

# 5. 调度器原理
print("\n5. 调度器原理")
print("-" * 40)

print("""
Ray调度器工作原理：

1. 任务提交：
   - 用户调用remote函数
   - 生成TaskSpec
   - 提交到本地Raylet

2. 依赖解析：
   - 检查输入ObjectRef
   - 等待依赖对象就绪
   - 构建任务依赖图

3. 资源匹配：
   - 检查资源需求
   - 查找满足条件的节点
   - 考虑数据本地性

4. 任务分发：
   - 选择最优节点
   - 传输任务代码和数据
   - 在Worker中执行

5. 结果处理：
   - 存储结果到Object Store
   - 更新依赖关系
   - 触发后续任务
""")

@ray.remote(num_cpus=1, memory=50*1024*1024)  # 50MB内存
def resource_aware_task(task_id: str, data_size_mb: int):
    """资源感知的任务"""
    import os
    
    # 分配指定大小的内存
    data = np.random.randn(data_size_mb * 1024 * 1024 // 8)  # 8字节per float64
    
    # 模拟计算
    result = np.sum(data)
    
    return {
        "task_id": task_id,
        "worker_pid": os.getpid(),
        "data_size_mb": data_size_mb,
        "result": float(result),
        "memory_usage": data.nbytes / 1024 / 1024
    }

# 测试资源调度
print("测试资源感知调度:")
resource_futures = [
    resource_aware_task.remote(f"resource_task_{i}", 10 + i * 5)
    for i in range(4)
]

resource_results = ray.get(resource_futures)
print("资源调度结果:")
for result in resource_results:
    print(f"  {result['task_id']}: PID={result['worker_pid']}, "
          f"内存={result['memory_usage']:.1f}MB")

# 6. 容错机制
print("\n6. 容错机制")
print("-" * 40)

print("""
Ray容错机制：

1. 任务重试：
   - 自动重试失败的任务
   - 可配置重试次数
   - 指数退避策略

2. Actor重启：
   - Actor崩溃后自动重启
   - 状态丢失但可重建
   - 支持检查点机制

3. 对象重建：
   - 基于血缘关系重建
   - 自动追踪依赖关系
   - 最小化重计算

4. 节点故障：
   - 自动检测节点故障
   - 迁移任务到其他节点
   - 重建丢失的对象
""")

@ray.remote(max_retries=2)
def unreliable_task(task_id: str, failure_rate: float = 0.3):
    """不可靠的任务，用于测试容错"""
    import random
    import os
    
    if random.random() < failure_rate:
        raise RuntimeError(f"任务 {task_id} 随机失败")
    
    return {
        "task_id": task_id,
        "worker_pid": os.getpid(),
        "status": "success"
    }

# 测试容错机制
print("测试容错机制:")
fault_futures = [
    unreliable_task.remote(f"fault_task_{i}", 0.4)
    for i in range(6)
]

successful_results = []
failed_count = 0

for future in fault_futures:
    try:
        result = ray.get(future)
        successful_results.append(result)
    except Exception as e:
        failed_count += 1
        print(f"  任务失败: {e}")

print(f"成功任务: {len(successful_results)}")
print(f"失败任务: {failed_count}")

# 7. 性能监控
print("\n7. 性能监控")
print("-" * 40)

def get_cluster_stats():
    """获取集群统计信息"""
    stats = {
        "cluster_resources": ray.cluster_resources(),
        "available_resources": ray.available_resources(),
        "nodes": len(ray.nodes()),
        "object_store_memory": ray.internal.internal_api.memory_summary(stats_only=True)
    }
    return stats

cluster_stats = get_cluster_stats()
print("集群统计信息:")
print(f"  节点数量: {cluster_stats['nodes']}")
print(f"  总CPU: {cluster_stats['cluster_resources'].get('CPU', 0)}")
print(f"  可用CPU: {cluster_stats['available_resources'].get('CPU', 0)}")

object_store_stats = cluster_stats['object_store_memory']
used_memory = object_store_stats.get('object_store_used_memory', 0)
total_memory = object_store_stats.get('object_store_total_memory', 0)

if total_memory > 0:
    print(f"  对象存储使用率: {used_memory / total_memory * 100:.1f}%")

print("\n" + "=" * 60)
print("Ray核心架构深度解析完成！")
print("=" * 60)
