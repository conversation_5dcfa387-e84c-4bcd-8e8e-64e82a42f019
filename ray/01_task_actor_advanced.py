#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ray Task和Actor高级用法
包含：Task链式调用、Actor池、动态资源分配、生命周期管理等
"""

import ray
import time
import numpy as np
import asyncio
from typing import List, Dict, Any, Optional
import threading
import queue
import random

print("=" * 60)
print("Ray Task和Actor高级用法")
print("=" * 60)

# 确保Ray已初始化
if not ray.is_initialized():
    ray.init(ignore_reinit_error=True)

# 1. Task链式调用和依赖管理
print("\n1. Task链式调用和依赖管理")
print("-" * 40)

@ray.remote
def data_preprocessing(raw_data: List[float]) -> np.ndarray:
    """数据预处理任务"""
    print(f"预处理 {len(raw_data)} 个数据点")
    # 模拟预处理：标准化
    data = np.array(raw_data)
    normalized = (data - np.mean(data)) / np.std(data)
    time.sleep(0.5)  # 模拟处理时间
    return normalized

@ray.remote
def feature_extraction(processed_data: np.ndarray) -> Dict[str, float]:
    """特征提取任务"""
    print(f"从 {len(processed_data)} 个数据点提取特征")
    features = {
        "mean": float(np.mean(processed_data)),
        "std": float(np.std(processed_data)),
        "min": float(np.min(processed_data)),
        "max": float(np.max(processed_data)),
        "median": float(np.median(processed_data))
    }
    time.sleep(0.3)  # 模拟处理时间
    return features

@ray.remote
def model_prediction(features: Dict[str, float]) -> Dict[str, Any]:
    """模型预测任务"""
    print("执行模型预测")
    # 模拟简单的预测逻辑
    score = features["mean"] * 0.5 + features["std"] * 0.3
    prediction = "positive" if score > 0 else "negative"
    
    result = {
        "prediction": prediction,
        "confidence": abs(score),
        "features_used": features
    }
    time.sleep(0.2)  # 模拟预测时间
    return result

# 演示Task链式调用
print("演示Task链式调用:")
raw_datasets = [
    [random.gauss(0, 1) for _ in range(100)],
    [random.gauss(2, 1.5) for _ in range(100)],
    [random.gauss(-1, 0.8) for _ in range(100)]
]

print("构建处理流水线...")
pipeline_futures = []

for i, raw_data in enumerate(raw_datasets):
    print(f"  启动数据集 {i+1} 的处理流水线")
    
    # 步骤1：数据预处理
    processed_future = data_preprocessing.remote(raw_data)
    
    # 步骤2：特征提取（依赖步骤1）
    features_future = feature_extraction.remote(processed_future)
    
    # 步骤3：模型预测（依赖步骤2）
    prediction_future = model_prediction.remote(features_future)
    
    pipeline_futures.append(prediction_future)

# 等待所有流水线完成
print("等待流水线完成...")
pipeline_results = ray.get(pipeline_futures)

print("流水线结果:")
for i, result in enumerate(pipeline_results):
    print(f"  数据集 {i+1}: {result['prediction']} "
          f"(置信度: {result['confidence']:.3f})")

# 2. Actor池模式
print("\n2. Actor池模式")
print("-" * 40)

@ray.remote
class WorkerActor:
    """工作Actor"""
    
    def __init__(self, worker_id: int):
        self.worker_id = worker_id
        self.tasks_completed = 0
        self.total_processing_time = 0.0
        print(f"Worker {worker_id} 初始化完成")
    
    def process_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理任务"""
        start_time = time.time()
        
        # 模拟任务处理
        task_type = task_data.get("type", "default")
        complexity = task_data.get("complexity", 1)
        
        # 根据复杂度调整处理时间
        processing_time = complexity * 0.1
        time.sleep(processing_time)
        
        # 模拟计算结果
        result = {
            "task_id": task_data.get("id"),
            "worker_id": self.worker_id,
            "result": f"Processed {task_type} task",
            "processing_time": processing_time,
            "timestamp": time.time()
        }
        
        # 更新统计信息
        self.tasks_completed += 1
        self.total_processing_time += processing_time
        
        return result
    
    def get_stats(self) -> Dict[str, Any]:
        """获取Worker统计信息"""
        avg_time = (self.total_processing_time / self.tasks_completed 
                   if self.tasks_completed > 0 else 0)
        
        return {
            "worker_id": self.worker_id,
            "tasks_completed": self.tasks_completed,
            "total_processing_time": self.total_processing_time,
            "average_processing_time": avg_time
        }

class ActorPool:
    """Actor池管理器"""
    
    def __init__(self, pool_size: int = 4):
        self.pool_size = pool_size
        self.actors = []
        self.current_index = 0
        
        # 创建Actor池
        print(f"创建包含 {pool_size} 个Actor的池")
        for i in range(pool_size):
            actor = WorkerActor.remote(i)
            self.actors.append(actor)
    
    def submit_task(self, task_data: Dict[str, Any]):
        """提交任务到池中的Actor"""
        # 轮询调度
        actor = self.actors[self.current_index]
        self.current_index = (self.current_index + 1) % self.pool_size
        
        return actor.process_task.remote(task_data)
    
    def get_all_stats(self):
        """获取所有Actor的统计信息"""
        stats_futures = [actor.get_stats.remote() for actor in self.actors]
        return ray.get(stats_futures)

# 测试Actor池
print("测试Actor池:")
actor_pool = ActorPool(pool_size=3)

# 生成测试任务
test_tasks = [
    {"id": f"task_{i}", "type": "compute", "complexity": random.randint(1, 5)}
    for i in range(12)
]

print(f"提交 {len(test_tasks)} 个任务到Actor池...")
task_futures = []
for task in test_tasks:
    future = actor_pool.submit_task(task)
    task_futures.append(future)

# 等待任务完成
task_results = ray.get(task_futures)

print("任务完成情况:")
for result in task_results[:5]:  # 只显示前5个结果
    print(f"  {result['task_id']}: Worker {result['worker_id']}, "
          f"耗时 {result['processing_time']:.2f}s")

# 获取Actor池统计信息
pool_stats = actor_pool.get_all_stats()
print("\nActor池统计:")
for stats in pool_stats:
    print(f"  Worker {stats['worker_id']}: "
          f"完成 {stats['tasks_completed']} 个任务, "
          f"平均耗时 {stats['average_processing_time']:.3f}s")

# 3. 动态资源分配
print("\n3. 动态资源分配")
print("-" * 40)

@ray.remote(num_cpus=1)
class DynamicResourceActor:
    """动态资源分配Actor"""
    
    def __init__(self, name: str):
        self.name = name
        self.current_load = 0
        self.max_load = 10
        print(f"DynamicResourceActor {name} 启动")
    
    def get_load(self) -> int:
        """获取当前负载"""
        return self.current_load
    
    def process_heavy_task(self, task_size: int) -> Dict[str, Any]:
        """处理重型任务"""
        if self.current_load + task_size > self.max_load:
            return {"status": "rejected", "reason": "overloaded"}
        
        self.current_load += task_size
        
        # 模拟处理时间
        processing_time = task_size * 0.1
        time.sleep(processing_time)
        
        self.current_load -= task_size
        
        return {
            "status": "completed",
            "task_size": task_size,
            "processing_time": processing_time,
            "actor_name": self.name
        }

class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self, num_actors: int = 3):
        self.actors = [
            DynamicResourceActor.remote(f"Actor_{i}")
            for i in range(num_actors)
        ]
    
    def find_best_actor(self, task_size: int):
        """找到最适合的Actor"""
        # 获取所有Actor的当前负载
        load_futures = [actor.get_load.remote() for actor in self.actors]
        loads = ray.get(load_futures)
        
        # 找到负载最低且能处理任务的Actor
        best_actor = None
        min_load = float('inf')
        
        for i, load in enumerate(loads):
            if load + task_size <= 10 and load < min_load:
                min_load = load
                best_actor = self.actors[i]
        
        return best_actor
    
    def submit_task(self, task_size: int):
        """提交任务"""
        actor = self.find_best_actor(task_size)
        if actor is None:
            return None  # 所有Actor都过载
        
        return actor.process_heavy_task.remote(task_size)

# 测试动态负载均衡
print("测试动态负载均衡:")
load_balancer = LoadBalancer(num_actors=3)

# 生成不同大小的任务
task_sizes = [3, 5, 2, 8, 1, 6, 4, 7, 2, 3]
print(f"提交任务大小: {task_sizes}")

submitted_futures = []
rejected_count = 0

for task_size in task_sizes:
    future = load_balancer.submit_task(task_size)
    if future is not None:
        submitted_futures.append(future)
    else:
        rejected_count += 1
        print(f"  任务大小 {task_size} 被拒绝（过载）")

# 等待任务完成
if submitted_futures:
    results = ray.get(submitted_futures)
    print(f"\n完成 {len(results)} 个任务，拒绝 {rejected_count} 个任务")
    
    # 按Actor分组统计
    actor_stats = {}
    for result in results:
        if result["status"] == "completed":
            actor_name = result["actor_name"]
            if actor_name not in actor_stats:
                actor_stats[actor_name] = {"count": 0, "total_time": 0}
            actor_stats[actor_name]["count"] += 1
            actor_stats[actor_name]["total_time"] += result["processing_time"]
    
    print("Actor处理统计:")
    for actor_name, stats in actor_stats.items():
        avg_time = stats["total_time"] / stats["count"]
        print(f"  {actor_name}: {stats['count']} 个任务, "
              f"平均耗时 {avg_time:.3f}s")

# 4. Actor生命周期管理
print("\n4. Actor生命周期管理")
print("-" * 40)

@ray.remote
class ManagedActor:
    """具有生命周期管理的Actor"""
    
    def __init__(self, name: str, max_tasks: int = 10):
        self.name = name
        self.max_tasks = max_tasks
        self.tasks_processed = 0
        self.start_time = time.time()
        self.is_healthy = True
        print(f"ManagedActor {name} 启动，最大任务数: {max_tasks}")
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        uptime = time.time() - self.start_time
        return {
            "name": self.name,
            "is_healthy": self.is_healthy,
            "uptime": uptime,
            "tasks_processed": self.tasks_processed,
            "remaining_capacity": self.max_tasks - self.tasks_processed
        }
    
    def process_task(self, task_data: Any) -> Dict[str, Any]:
        """处理任务"""
        if self.tasks_processed >= self.max_tasks:
            self.is_healthy = False
            return {"status": "error", "reason": "max_tasks_reached"}
        
        if not self.is_healthy:
            return {"status": "error", "reason": "actor_unhealthy"}
        
        # 模拟任务处理
        time.sleep(0.1)
        self.tasks_processed += 1
        
        # 模拟偶发错误
        if random.random() < 0.1:  # 10%概率出错
            self.is_healthy = False
            return {"status": "error", "reason": "processing_failed"}
        
        return {
            "status": "success",
            "result": f"Task processed by {self.name}",
            "task_number": self.tasks_processed
        }
    
    def shutdown(self) -> str:
        """优雅关闭"""
        print(f"ManagedActor {self.name} 正在关闭...")
        self.is_healthy = False
        return f"Actor {self.name} 已关闭"

class ActorManager:
    """Actor管理器"""
    
    def __init__(self):
        self.actors = {}
        self.actor_counter = 0
    
    def create_actor(self, max_tasks: int = 10):
        """创建新Actor"""
        actor_name = f"managed_actor_{self.actor_counter}"
        self.actor_counter += 1
        
        actor = ManagedActor.remote(actor_name, max_tasks)
        self.actors[actor_name] = actor
        
        print(f"创建Actor: {actor_name}")
        return actor_name
    
    def health_check_all(self) -> Dict[str, Dict]:
        """检查所有Actor的健康状态"""
        health_futures = {
            name: actor.health_check.remote()
            for name, actor in self.actors.items()
        }
        
        health_results = {}
        for name, future in health_futures.items():
            try:
                health_results[name] = ray.get(future)
            except Exception as e:
                health_results[name] = {"error": str(e)}
        
        return health_results
    
    def remove_unhealthy_actors(self):
        """移除不健康的Actor"""
        health_results = self.health_check_all()
        
        unhealthy_actors = []
        for name, health in health_results.items():
            if not health.get("is_healthy", False):
                unhealthy_actors.append(name)
        
        for name in unhealthy_actors:
            if name in self.actors:
                # 尝试优雅关闭
                try:
                    ray.get(self.actors[name].shutdown.remote())
                except:
                    pass
                
                del self.actors[name]
                print(f"移除不健康的Actor: {name}")
        
        return len(unhealthy_actors)
    
    def get_healthy_actor(self):
        """获取一个健康的Actor"""
        health_results = self.health_check_all()
        
        for name, health in health_results.items():
            if health.get("is_healthy", False) and health.get("remaining_capacity", 0) > 0:
                return self.actors[name]
        
        return None

# 测试Actor生命周期管理
print("测试Actor生命周期管理:")
actor_manager = ActorManager()

# 创建几个Actor
for _ in range(3):
    actor_manager.create_actor(max_tasks=5)

print(f"创建了 {len(actor_manager.actors)} 个Actor")

# 提交任务直到Actor不健康
task_count = 0
max_attempts = 20

for i in range(max_attempts):
    healthy_actor = actor_manager.get_healthy_actor()
    
    if healthy_actor is None:
        print("没有健康的Actor可用，创建新的Actor")
        actor_manager.create_actor(max_tasks=5)
        healthy_actor = actor_manager.get_healthy_actor()
    
    if healthy_actor is not None:
        try:
            result = ray.get(healthy_actor.process_task.remote(f"task_{i}"))
            if result["status"] == "success":
                task_count += 1
            else:
                print(f"任务失败: {result}")
        except Exception as e:
            print(f"任务执行异常: {e}")
    
    # 定期清理不健康的Actor
    if i % 5 == 0:
        removed_count = actor_manager.remove_unhealthy_actors()
        if removed_count > 0:
            print(f"清理了 {removed_count} 个不健康的Actor")

print(f"\n总共成功处理 {task_count} 个任务")

# 最终健康检查
final_health = actor_manager.health_check_all()
print("最终Actor状态:")
for name, health in final_health.items():
    if "error" not in health:
        print(f"  {name}: 健康={health['is_healthy']}, "
              f"已处理={health['tasks_processed']}, "
              f"剩余容量={health['remaining_capacity']}")

print("\n" + "=" * 60)
print("Ray Task和Actor高级用法学习完成！")
print("=" * 60)
